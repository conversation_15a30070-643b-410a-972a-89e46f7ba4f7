# jCloud生产环境配置文件
# 此文件包含生产环境的敏感信息，请勿提交到版本控制系统

# ==================== 基础配置 ====================
COMPOSE_PROJECT_NAME=jcloud
ENVIRONMENT=production

# ==================== 数据库配置 ====================
# 使用外部生产数据库，不启动Docker中的MySQL服务
DB_HOST=*************
DB_PORT=3306
DB_MASTER_NAME=cs2_skin_platform
DB_SLAVE_NAME=vimbox
DB_USERNAME=voltskins
DB_PASSWORD=ShbAeEVw7RNh8arDzjN4eZhsh@

# ==================== Redis配置 ====================
# 使用外部生产Redis，不启动Docker中的Redis服务
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD=DAANFtJj3n5PtbM8zDkPwh5PG
REDIS_DATABASE=10

# ==================== 应用端口配置 ====================
# 后端服务端口
BACKEND_PORT=8081

# 前端服务端口
FRONTEND_PORT=80

# Nginx反向代理端口（可选）
NGINX_PORT=8080

# ==================== JVM配置 ====================
# 生产环境JVM参数
JAVA_OPTS=-Xms1g -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=200 -Djava.security.egd=file:/dev/./urandom -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai

# ==================== 日志配置 ====================
LOG_LEVEL=info
LOG_PATH=/app/logs

# ==================== 文件上传配置 ====================
FILE_UPLOAD_PATH=/app/uploads
FILE_MAX_SIZE=10MB

# ==================== 安全配置 ====================
# Spring Profile
SPRING_PROFILES_ACTIVE=prod

# ==================== 监控配置 ====================
# 是否启用Druid监控（生产环境建议关闭）
DRUID_MONITOR_ENABLED=false

# 是否启用API文档（生产环境建议关闭）
API_DOC_ENABLED=false

# ==================== 时区配置 ====================
TZ=Asia/Shanghai

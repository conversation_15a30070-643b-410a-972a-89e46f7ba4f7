# jCloud项目生产环境部署指南

## 概述

本文档详细说明如何将jCloud项目部署到生产服务器。项目采用Docker容器化部署，支持通过1Panel面板进行管理。

## 技术栈

- **后端**: Java 21 + Spring Boot 3.2.1 + MyBatis-Flex + sa-token
- **前端**: React 19 + TypeScript + Vite + Tailwind CSS + Shadcn UI
- **数据库**: MySQL 8.0.33（外部生产数据库）
- **缓存**: Redis 7.2（外部生产Redis）
- **容器**: Docker + Docker Compose
- **Web服务器**: Nginx（前端静态文件服务和API代理）

## 前置条件

### 服务器环境要求

- **操作系统**: Linux（推荐Ubuntu 20.04+或CentOS 8+）
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低20GB可用空间
- **网络**: 确保可以访问外部数据库和Redis服务器

### 已安装软件

- [x] Docker 20.10+
- [x] Docker Compose 2.0+
- [x] 1Panel Linux服务器运维管理面板

### 外部服务

- [x] MySQL数据库服务器（*************:3306）
- [x] Redis缓存服务器（*************:6379）

## 部署步骤

### 1. 准备项目文件

将项目文件上传到服务器，建议路径：`/opt/jcloud`

```bash
# 创建项目目录
sudo mkdir -p /opt/jcloud
cd /opt/jcloud

# 上传项目文件（通过git clone、scp或1Panel文件管理器）
# git clone <your-repository-url> .
```

### 2. 配置环境变量

项目已包含生产环境配置文件 `.env.prod`，包含以下关键配置：

```bash
# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_MASTER_NAME=cs2_skin_platform
DB_SLAVE_NAME=vimbox
DB_USERNAME=voltskins
DB_PASSWORD=ShbAeEVw7RNh8arDzjN4eZhsh@

# Redis配置
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD=DAANFtJj3n5PtbM8zDkPwh5PG
REDIS_DATABASE=10

# 应用端口
BACKEND_PORT=8081
FRONTEND_PORT=80
```

### 3. 构建Docker镜像

```bash
# 使用构建脚本
./scripts/build.sh

# 或手动构建
docker-compose -f docker-compose.prod.yml build
```

### 4. 部署服务

```bash
# 使用部署脚本（推荐）
./scripts/deploy.sh

# 或手动部署
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### 5. 验证部署

部署完成后，验证各服务是否正常运行：

```bash
# 检查容器状态
docker-compose -f docker-compose.prod.yml ps

# 检查服务健康状态
curl http://localhost:8081/api/actuator/health  # 后端健康检查
curl http://localhost/health                    # 前端健康检查

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## 1Panel面板集成

### 导入Docker Compose项目

1. 登录1Panel管理面板
2. 进入"容器" -> "编排"
3. 点击"创建编排"
4. 选择"从现有compose文件创建"
5. 上传 `docker-compose.prod.yml` 文件
6. 配置环境变量文件 `.env.prod`
7. 点击"创建并启动"

### 容器管理

在1Panel面板中可以进行以下操作：

- **启动/停止/重启容器**
- **查看容器日志**
- **监控资源使用情况**
- **更新容器镜像**
- **备份和恢复数据卷**

## 端口映射

| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| 前端 | 80 | 80 | Web界面访问 |
| 后端 | 8081 | 8081 | API接口访问 |

## 数据持久化

项目配置了以下数据卷：

- `backend-logs`: 后端应用日志
- `backend-uploads`: 文件上传存储

数据卷位置：`/var/lib/docker/volumes/jcloud_*`

## 服务管理脚本

项目提供了便捷的管理脚本：

```bash
# 构建镜像
./scripts/build.sh

# 部署服务
./scripts/deploy.sh

# 停止服务
./scripts/stop.sh

# 重启服务
./scripts/restart.sh
```

## 访问地址

部署成功后，可通过以下地址访问：

- **前端界面**: http://服务器IP:80
- **后端API**: http://服务器IP:8081/api
- **健康检查**: http://服务器IP:8081/api/actuator/health
- **API文档**: http://服务器IP:8081/api/doc.html（如果启用）

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose -f docker-compose.prod.yml logs
   
   # 检查容器状态
   docker ps -a
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   telnet ************* 3306
   
   # 检查网络连通性
   ping *************
   ```

3. **Redis连接失败**
   ```bash
   # 测试Redis连接
   telnet ************* 6379
   ```

4. **前端无法访问后端API**
   - 检查nginx配置中的upstream设置
   - 确认后端服务健康状态
   - 查看nginx错误日志

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs backend
docker-compose -f docker-compose.prod.yml logs frontend

# 实时查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## 更新部署

### 更新应用代码

1. 更新代码
2. 重新构建镜像：`./scripts/build.sh`
3. 重新部署：`./scripts/restart.sh`

### 更新配置

1. 修改 `.env.prod` 文件
2. 重启服务：`./scripts/restart.sh`

## 安全建议

1. **网络安全**
   - 配置防火墙，仅开放必要端口
   - 使用HTTPS（配置SSL证书）
   - 限制数据库和Redis的访问IP

2. **容器安全**
   - 定期更新基础镜像
   - 使用非root用户运行容器
   - 限制容器资源使用

3. **数据安全**
   - 定期备份数据库
   - 加密敏感配置信息
   - 监控异常访问

## 监控和维护

1. **性能监控**
   - 使用1Panel监控容器资源使用
   - 监控数据库连接池状态
   - 关注应用响应时间

2. **日志管理**
   - 定期清理旧日志文件
   - 配置日志轮转
   - 监控错误日志

3. **备份策略**
   - 定期备份数据卷
   - 备份配置文件
   - 测试恢复流程

## 联系支持

如遇到部署问题，请提供以下信息：

- 服务器环境信息
- 错误日志
- 容器状态
- 网络连接测试结果

---

**注意**: 本文档基于当前项目配置编写，如有配置变更请及时更新文档。

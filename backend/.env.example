# jCloud 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# 数据库配置 - 主库
DB_HOST=*************
DB_PORT=3306
DB_NAME=cs2_skin_platform
DB_USERNAME=voltskins
DB_PASSWORD=your_database_password_here

# 数据库配置 - 从库（用于读操作）
DB_SLAVE_HOST=*************
DB_SLAVE_PORT=3306
DB_SLAVE_NAME=vimbox_test
DB_SLAVE_USERNAME=voltskins
DB_SLAVE_PASSWORD=your_slave_database_password_here

# Redis配置
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# Druid监控配置
DRUID_USERNAME=admin
DRUID_PASSWORD=your_druid_password_here

# 应用配置
SPRING_PROFILES_ACTIVE=prod

# 缓存配置
CACHE_ENABLED=true

# 安全提示：
# 1. 请勿将此文件提交到版本控制系统
# 2. 生产环境建议使用更强的密码
# 3. 定期更换密码
# 4. 限制数据库和Redis的网络访问

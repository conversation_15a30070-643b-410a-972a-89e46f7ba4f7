package com.jcloud.admin.constant;

/**
 * 运营模块常量定义
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class OperationsConstants {
    
    /**
     * 用户身份类型
     */
    public static class UserIdentity {
        /** 普通用户 */
        public static final int USER = 1;
        /** 线上主播 */
        public static final int ONLINE_ANCHOR = 2;
        /** 线下主播 */
        public static final int OFFLINE_ANCHOR = 3;
    }
    
    /**
     * 用户状态
     */
    public static class UserState {
        /** 正常 */
        public static final int NORMAL = 1;
        /** 禁用 */
        public static final int DISABLED = 2;
    }
    
    /**
     * 实名认证状态
     */
    public static class AuthStatus {
        /** 未实名 */
        public static final int NOT_AUTH = 0;
        /** 已实名 */
        public static final int AUTHED = 1;
    }
    
    /**
     * 充值订单状态
     */
    public static class RechargeState {
        /** 未支付 */
        public static final int UNPAID = 1;
        /** 已支付 */
        public static final int PAID = 2;
        /** 已支付但回调异常 */
        public static final int PAID_CALLBACK_ERROR = 3;
        
        /**
         * 获取状态描述
         */
        public static String getStateDesc(Integer state) {
            if (state == null) {
                return "未知";
            }
            return switch (state) {
                case UNPAID -> "未支付";
                case PAID -> "已支付";
                case PAID_CALLBACK_ERROR -> "已支付但回调异常";
                default -> "未知";
            };
        }
    }
    
    /**
     * 权限标识
     */
    public static class Permission {
        /** 主播查询权限 */
        public static final String ANCHOR_QUERY = "operations:anchor:query";
        /** 主播详情权限 */
        public static final String ANCHOR_DETAIL = "operations:anchor:detail";
        /** 主播首充统计权限 */
        public static final String ANCHOR_FIRST_RECHARGE = "operations:anchor:first-recharge";
        /** 主播下级用户权限 */
        public static final String ANCHOR_USERS = "operations:anchor:users";
        /** 用户消费详情权限 */
        public static final String USER_CONSUME = "operations:user:consume";
        /** 用户充值详情权限 */
        public static final String USER_RECHARGE = "operations:user:recharge";
    }
    
    /**
     * 数据源配置
     */
    public static class DataSource {
        /** 从库数据源名称 */
        public static final String SLAVE = "slave";
        /** vimbox数据库 */
        public static final String VIMBOX = "vimbox";
    }
    
    /**
     * 存储过程名称
     */
    public static class StoredProcedure {
        /** 获取邀请用户统计 */
        public static final String GET_INVITE_USER_STATISTICS = "GetInviteUserStatistics";
    }
    
    /**
     * 消费类型推断关键词
     */
    public static class ConsumeType {
        /** 购买道具 */
        public static final String BUY_ITEM = "购买";
        /** 开箱消费 */
        public static final String OPEN_BOX = "开箱";
        /** 锻造消费 */
        public static final String FORGE = "锻造";
        /** 其他消费 */
        public static final String OTHER = "其他";
        
        /**
         * 根据消费说明推断消费类型
         */
        public static String inferType(String info) {
            if (info == null || info.trim().isEmpty()) {
                return OTHER;
            }
            String lowerInfo = info.toLowerCase();
            if (lowerInfo.contains("购买") || lowerInfo.contains("买")) {
                return BUY_ITEM;
            } else if (lowerInfo.contains("开箱") || lowerInfo.contains("开启")) {
                return OPEN_BOX;
            } else if (lowerInfo.contains("锻造") || lowerInfo.contains("合成")) {
                return FORGE;
            } else {
                return OTHER;
            }
        }
    }
    
    /**
     * 支付方式推断
     */
    public static class PaymentMethod {
        /** 微信支付 */
        public static final String WECHAT = "微信支付";
        /** 支付宝 */
        public static final String ALIPAY = "支付宝";
        /** 其他支付方式 */
        public static final String OTHER = "其他";
        
        /**
         * 根据三方订单号推断支付方式
         */
        public static String inferMethod(String payId) {
            if (payId == null || payId.trim().isEmpty()) {
                return OTHER;
            }
            String lowerPayId = payId.toLowerCase();
            if (lowerPayId.startsWith("wx") || lowerPayId.contains("wechat")) {
                return WECHAT;
            } else if (lowerPayId.startsWith("ali") || lowerPayId.contains("alipay")) {
                return ALIPAY;
            } else {
                return OTHER;
            }
        }
    }
    
    /**
     * 默认分页参数
     */
    public static class Page {
        /** 默认页码 */
        public static final int DEFAULT_PAGE_NUM = 1;
        /** 默认每页大小 */
        public static final int DEFAULT_PAGE_SIZE = 10;
        /** 最大每页大小 */
        public static final int MAX_PAGE_SIZE = 100;
    }
}

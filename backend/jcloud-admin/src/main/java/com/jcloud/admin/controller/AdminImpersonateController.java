package com.jcloud.admin.controller;

import com.jcloud.admin.service.ImpersonationService;
import com.jcloud.common.annotation.OperLog;

import com.jcloud.common.dto.ImpersonationRequest;
import com.jcloud.common.dto.ImpersonationResponse;
import com.jcloud.common.dto.ImpersonationStatusResponse;
import com.jcloud.common.enums.BusinessType;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员模拟登录控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/admin/impersonate")
@RequiredArgsConstructor
@Tag(name = "管理员模拟登录", description = "超级管理员模拟登录功能")
public class AdminImpersonateController {
    
    private final ImpersonationService impersonationService;
    
    /**
     * 开始模拟登录
     */
    @PostMapping("/start")
    @Operation(summary = "开始模拟登录", description = "超级管理员开始模拟指定用户登录")
    @OperLog(title = "开始模拟登录", businessType = 10)
    public Result<ImpersonationResponse> startImpersonation(@Validated @RequestBody ImpersonationRequest request) {
        log.info("开始模拟登录请求: targetUserId={}, reason={}", request.getTargetUserId(), request.getReason());
        
        ImpersonationResponse response = impersonationService.startImpersonation(request);
        
        log.info("模拟登录成功: targetUserId={}, token={}", 
                response.getTargetUserId(), 
                response.getToken() != null ? response.getToken().substring(0, Math.min(10, response.getToken().length())) + "..." : "null");
        
        return Result.success(response);
    }
    
    /**
     * 停止模拟登录
     */
    @PostMapping("/stop")
    @Operation(summary = "停止模拟登录", description = "结束当前模拟登录状态，返回管理员身份")
    @OperLog(title = "停止模拟登录", businessType = 10)
    public Result<ImpersonationResponse> stopImpersonation() {
        log.info("停止模拟登录请求");
        
        ImpersonationResponse response = impersonationService.stopImpersonation();
        
        log.info("停止模拟登录成功: adminUserId={}, token={}", 
                response.getAdminUserId(),
                response.getToken() != null ? response.getToken().substring(0, Math.min(10, response.getToken().length())) + "..." : "null");
        
        return Result.success(response);
    }
    
    /**
     * 获取模拟登录状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取模拟登录状态", description = "获取当前模拟登录状态信息")
    public Result<ImpersonationStatusResponse> getImpersonationStatus() {
        ImpersonationStatusResponse response = impersonationService.getImpersonationStatus();
        
        log.debug("获取模拟登录状态: impersonating={}, targetUserId={}", 
                response.getImpersonating(), response.getTargetUserId());
        
        return Result.success(response);
    }
    
    /**
     * 检查用户是否可以被模拟
     */
    @GetMapping("/can-impersonate/{userId}")
    @Operation(summary = "检查用户是否可以被模拟", description = "检查指定用户是否允许被模拟登录")

    public Result<Boolean> canImpersonate(@PathVariable("userId") Long userId) {
        boolean canImpersonate = impersonationService.canImpersonate(userId);
        
        log.debug("检查用户是否可以被模拟: userId={}, canImpersonate={}", userId, canImpersonate);
        
        return Result.success(canImpersonate);
    }
}

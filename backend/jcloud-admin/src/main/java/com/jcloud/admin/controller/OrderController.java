package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.common.dto.OrderCreateRequest;
import com.jcloud.common.dto.OrderQueryRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import com.jcloud.common.service.OrderService;
import com.jcloud.common.vo.OrderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 佣金结算订单控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
@Tag(name = "佣金结算订单管理", description = "代理和主播佣金结算订单管理接口")
public class OrderController {
    
    private final OrderService orderService;
    
    /**
     * 创建佣金结算订单
     */
    @PostMapping
    @Operation(summary = "创建佣金结算订单", description = "根据时间区间和劳务比例创建佣金结算订单")
    @SaCheckPermission("commission:order:create")
    public Result<List<SysOrder>> createOrders(@Valid @RequestBody OrderCreateRequest request) {
        log.info("创建佣金结算订单请求: {}", request);
        
        List<SysOrder> orders = orderService.createOrders(request);
        
        log.info("佣金结算订单创建成功，共创建 {} 个订单", orders.size());
        return Result.success(orders);
    }
    
    /**
     * 分页查询订单列表
     */
    @GetMapping
    @Operation(summary = "分页查询订单列表", description = "支持多条件筛选的订单分页查询")
    @SaCheckPermission("commission:order:list")
    public Result<PageResult<OrderVO>> pageOrders(@Valid OrderQueryRequest request) {
        log.info("分页查询订单列表请求: {}", request);
        
        PageResult<OrderVO> result = orderService.pageOrders(request);
        
        log.info("订单列表查询成功，共 {} 条记录", result.getTotal());
        return Result.success(result);
    }

    /**
     * 获取指定主订单的子订单列表
     */
    @GetMapping("/{mainOrderId}/sub-orders")
    @Operation(summary = "获取子订单列表", description = "获取指定主订单的所有子订单")
    @SaCheckPermission("commission:order:list")
    public Result<List<OrderVO>> getSubOrders(@PathVariable String mainOrderId) {
        log.info("获取子订单列表请求，主订单ID: {}", mainOrderId);

        List<OrderVO> subOrders = orderService.getSubOrdersByMainOrderId(mainOrderId);

        log.info("子订单列表查询成功，共 {} 条记录", subOrders.size());
        return Result.success(subOrders);
    }
    
    /**
     * 查询订单详情
     */
    @GetMapping("/{payid}")
    @Operation(summary = "查询订单详情", description = "根据订单ID查询订单详细信息")
    @SaCheckPermission("commission:order:detail")
    public Result<OrderVO> getOrderDetail(
            @Parameter(description = "订单ID", required = true)
            @PathVariable String payid) {
        log.info("查询订单详情，订单ID: {}", payid);
        
        OrderVO order = orderService.getOrderDetail(payid);
        
        log.info("订单详情查询成功");
        return Result.success(order);
    }
    
    /**
     * 查询主订单及其子订单
     */
    @GetMapping("/{parentsPayid}/tree")
    @Operation(summary = "查询主订单及其子订单", description = "查询主订单及其所有子订单的树形结构")
    @SaCheckPermission("commission:order:detail")
    public Result<OrderVO> getMainOrderWithSubOrders(
            @Parameter(description = "主订单ID", required = true)
            @PathVariable String parentsPayid) {
        log.info("查询主订单及子订单，主订单ID: {}", parentsPayid);
        
        OrderVO orderTree = orderService.getMainOrderWithSubOrders(parentsPayid);
        
        log.info("主订单及子订单查询成功，子订单数量: {}", orderTree.getSubOrderCount());
        return Result.success(orderTree);
    }
    
    /**
     * 批量结算订单
     */
    @PutMapping("/settle")
    @Operation(summary = "批量结算订单", description = "批量将订单状态更新为已结算")
    @SaCheckPermission("commission:order:settle")
    public Result<Integer> settleOrders(@RequestBody List<String> payids) {
        log.info("批量结算订单请求，订单数量: {}", payids.size());
        
        int settledCount = orderService.settleOrders(payids);
        
        log.info("批量结算完成，成功结算 {} 个订单", settledCount);
        return Result.success(settledCount);
    }
    
    /**
     * 取消订单
     */
    @PutMapping("/{payid}/cancel")
    @Operation(summary = "取消订单", description = "将订单状态更新为已取消")
    @SaCheckPermission("commission:order:cancel")
    public Result<Boolean> cancelOrder(
            @Parameter(description = "订单ID", required = true)
            @PathVariable String payid) {
        log.info("取消订单请求，订单ID: {}", payid);
        
        boolean success = orderService.cancelOrder(payid);
        
        log.info("订单取消{}", success ? "成功" : "失败");
        return Result.success(success);
    }
    
    /**
     * 批量取消订单
     */
    @PutMapping("/cancel")
    @Operation(summary = "批量取消订单", description = "批量将订单状态更新为已取消")
    @SaCheckPermission("commission:order:cancel")
    public Result<Integer> cancelOrders(@RequestBody List<String> payids) {
        log.info("批量取消订单请求，订单数量: {}", payids.size());
        
        int cancelledCount = orderService.cancelOrders(payids);
        
        log.info("批量取消完成，成功取消 {} 个订单", cancelledCount);
        return Result.success(cancelledCount);
    }
    
    /**
     * 检查是否可以创建订单
     */
    @PostMapping("/check")
    @Operation(summary = "检查是否可以创建订单", description = "验证订单创建条件是否满足")
    @SaCheckPermission("commission:order:create")
    public Result<Boolean> canCreateOrder(@Valid @RequestBody OrderCreateRequest request) {
        log.info("检查订单创建条件请求: {}", request);
        
        boolean canCreate = orderService.canCreateOrder(request);
        
        log.info("订单创建条件检查结果: {}", !canCreate);
        return Result.success(!canCreate);
    }
    
    /**
     * 获取订单统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取订单统计信息", description = "获取当前用户的订单统计数据")
    @SaCheckPermission("commission:order:statistics")
    public Result<OrderService.OrderStatistics> getOrderStatistics() {
        log.info("获取订单统计信息请求");
        
        OrderService.OrderStatistics statistics = orderService.getOrderStatistics();
        
        log.info("订单统计信息查询成功");
        return Result.success(statistics);
    }
    /**
     * 导出订单数据为Excel文件
     */
    @PostMapping(value = "/export/excel")
    @Operation(summary = "导出订单Excel文件", description = "根据查询条件导出订单数据为Excel文件")
    @SaCheckPermission("commission:order:export")
    public ResponseEntity<byte[]> exportOrdersToExcel(@Valid @RequestBody OrderQueryRequest request) {
        log.info("🚀 开始导出订单Excel文件，请求参数: {}", request);

        try {
            // 调用服务生成Excel文件
            byte[] excelData = orderService.exportOrdersToExcel(request);

            if (excelData == null || excelData.length == 0) {
                log.error("❌ Excel文件生成失败：返回的数据为空");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Excel文件生成失败".getBytes());
            }

            String filename = "代理结算_" + System.currentTimeMillis() + ".xlsx";

            HttpHeaders headers = new HttpHeaders();
            // 设置正确的Excel MIME类型
            headers.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // 设置文件下载头
            headers.add("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            // 设置文件大小
            headers.add("Content-Length", String.valueOf(excelData.length));
            // 允许跨域访问文件名
            headers.add("Access-Control-Expose-Headers", "Content-Disposition");

            log.info("✅ Excel文件生成成功！文件名: {}, 文件大小: {} bytes", filename, excelData.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);
        } catch (Exception e) {
            log.error("❌ 导出订单Excel文件失败", e);

            // 返回错误信息，但不要返回JSON格式
            String errorMessage = "Excel文件生成失败: " + e.getMessage();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("Content-Type", "text/plain")
                    .body(errorMessage.getBytes());
        }
    }
}

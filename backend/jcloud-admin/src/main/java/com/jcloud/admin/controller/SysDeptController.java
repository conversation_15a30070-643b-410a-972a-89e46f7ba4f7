package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.SysDeptService;
import com.jcloud.admin.service.DeptPermissionService;
import com.jcloud.common.dto.BatchDeptUserRequest;
import com.jcloud.common.dto.DeptCreateRequest;
import com.jcloud.common.dto.DeptQueryRequest;
import com.jcloud.common.dto.DeptUpdateRequest;

import com.jcloud.common.entity.SysDept;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "部门管理", description = "部门管理相关接口")
@RestController
@RequestMapping("/system/dept")
@RequiredArgsConstructor
@Slf4j
public class SysDeptController {

    private final SysDeptService deptService;
    private final DeptPermissionService deptPermissionService;
    
    @Operation(summary = "分页查询部门列表", description = "根据条件分页查询部门列表")
    @SaCheckPermission("system:dept:list")
    @GetMapping("/page")
    public Result<PageResult<SysDept>> pageDepts(@Valid DeptQueryRequest queryRequest) {
        PageResult<SysDept> pageResult = deptService.pageDepts(queryRequest);
        return Result.success("查询部门列表成功", pageResult);
    }
    
    @Operation(summary = "获取部门详情", description = "根据部门ID获取部门详细信息")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/{id}")
    public Result<SysDept> getDeptById(@Parameter(description = "部门ID") @PathVariable("id") Long id) {
        SysDept dept = deptService.getById(id);
        if (dept == null) {
            return Result.error("部门不存在");
        }
        return Result.success("获取部门详情成功", dept);
    }
    
    @Operation(summary = "创建部门", description = "创建新部门")
    @SaCheckPermission("system:dept:add")
    @PostMapping
    public Result<Void> createDept(@Valid @RequestBody DeptCreateRequest createRequest) {
        boolean success = deptService.createDept(createRequest);
        if (success) {
            return Result.<Void>success("创建部门成功", null);
        } else {
            return Result.error("创建部门失败");
        }
    }
    
    @Operation(summary = "更新部门", description = "更新部门信息")
    @SaCheckPermission("system:dept:edit")
    @PutMapping
    public Result<Void> updateDept(@Valid @RequestBody DeptUpdateRequest updateRequest) {
        boolean success = deptService.updateDept(updateRequest);
        if (success) {
            return Result.<Void>success("更新部门成功", null);
        } else {
            return Result.error("更新部门失败");
        }
    }
    
    @Operation(summary = "删除部门", description = "根据部门ID删除部门（逻辑删除）")
    @SaCheckPermission("system:dept:delete")
    @DeleteMapping("/{id}")
    public Result<Void> deleteDept(@Parameter(description = "部门ID") @PathVariable("id") Long id) {
        boolean success = deptService.deleteDept(id);
        if (success) {
            return Result.<Void>success("删除部门成功", null);
        } else {
            return Result.error("删除部门失败");
        }
    }
    
    @Operation(summary = "批量删除部门", description = "根据部门ID列表批量删除部门")
    @SaCheckPermission("system:dept:delete")
    @DeleteMapping("/batch")
    public Result<Void> deleteDeptsBatch(@Parameter(description = "部门ID列表") @RequestBody List<Long> deptIds) {
        boolean success = deptService.deleteDeptsBatch(deptIds);
        if (success) {
            return Result.<Void>success("批量删除部门成功", null);
        } else {
            return Result.error("批量删除部门失败");
        }
    }
    
    @Operation(summary = "启用/禁用部门", description = "更新部门状态")
    @SaCheckPermission("system:dept:edit")
    @PutMapping("/{id}/status")
    public Result<Void> updateDeptStatus(
            @Parameter(description = "部门ID") @PathVariable("id") Long id,
            @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam("status") Integer status) {
        boolean success = deptService.updateDeptStatus(id, status);
        if (success) {
            return Result.<Void>success("更新部门状态成功", null);
        } else {
            return Result.error("更新部门状态失败");
        }
    }
    
    @Operation(summary = "批量启用/禁用部门", description = "批量更新部门状态")
    @SaCheckPermission("system:dept:edit")
    @PutMapping("/batch/status")
    public Result<Void> updateDeptStatusBatch(
            @Parameter(description = "部门ID列表") @RequestParam List<Long> deptIds,
            @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam Integer status) {
        boolean success = deptService.updateDeptStatusBatch(deptIds, status);
        if (success) {
            return Result.<Void>success("批量更新部门状态成功", null);
        } else {
            return Result.error("批量更新部门状态失败");
        }
    }
    
    @Operation(summary = "获取部门树形结构", description = "获取完整的部门树形结构")
    @SaCheckPermission("system:dept:list")
    @GetMapping("/tree")
    public Result<List<SysDept>> getDeptTree() {
        List<SysDept> deptTree = deptService.buildDeptTree();
        return Result.success("获取部门树成功", deptTree);
    }
    
    @Operation(summary = "根据父部门ID获取子部门", description = "获取指定父部门下的子部门列表")
    @SaCheckPermission("system:dept:list")
    @GetMapping("/children/{parentId}")
    public Result<List<SysDept>> getDeptsByParentId(@Parameter(description = "父部门ID") @PathVariable("parentId") Long parentId) {
        List<SysDept> depts = deptService.getDeptsByParentId(parentId);
        return Result.success("获取子部门列表成功", depts);
    }
    
    @Operation(summary = "获取所有启用的部门", description = "获取所有启用状态的部门列表")
    @SaCheckPermission("system:dept:list")
    @GetMapping("/enabled")
    public Result<List<SysDept>> getAllEnabledDepts() {
        List<SysDept> depts = deptService.getAllEnabledDepts();
        return Result.success("获取启用部门列表成功", depts);
    }
    
    @Operation(summary = "根据用户ID获取部门列表", description = "获取指定用户所属的部门列表")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/user/{userId}")
    public Result<List<SysDept>> getDeptsByUserId(@Parameter(description = "用户ID") @PathVariable("userId") Long userId) {
        List<SysDept> depts = deptService.getDeptsByUserId(userId);
        return Result.success("获取用户部门列表成功", depts);
    }
    
    @Operation(summary = "根据部门ID获取用户列表", description = "获取指定部门下的用户列表")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/{id}/users")
    public Result<List<SysUser>> getUsersByDeptId(@Parameter(description = "部门ID") @PathVariable("id") Long id) {
        List<SysUser> users = deptService.getUsersByDeptId(id);
        return Result.success("获取部门用户列表成功", users);
    }
    
    @Operation(summary = "移动部门", description = "将部门移动到新的父部门下")
    @SaCheckPermission("system:dept:edit")
    @PutMapping("/{id}/move")
    public Result<Void> moveDept(
            @Parameter(description = "部门ID") @PathVariable("id") Long id,
            @Parameter(description = "新父部门ID") @RequestParam("newParentId") Long newParentId) {
        boolean success = deptService.moveDept(id, newParentId);
        if (success) {
            return Result.<Void>success("移动部门成功", null);
        } else {
            return Result.error("移动部门失败");
        }
    }
    
    @Operation(summary = "统计子部门数量", description = "统计指定部门下的子部门数量")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/{id}/child-count")
    public Result<Integer> countChildDepts(@Parameter(description = "部门ID") @PathVariable("id") Long id) {
        int count = deptService.countChildDepts(id);
        return Result.success("获取子部门数量成功", count);
    }
    
    @Operation(summary = "统计部门用户数量", description = "统计指定部门下的用户数量")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/{id}/user-count")
    public Result<Integer> countUsersByDeptId(@Parameter(description = "部门ID") @PathVariable("id") Long id) {
        int count = deptService.countUsersByDeptId(id);
        return Result.success("获取部门用户数量成功", count);
    }
    
    @Operation(summary = "分配用户到部门", description = "将用户分配到指定的部门列表")
    @SaCheckPermission("system:dept:edit")
    @PostMapping("/assign-user")
    public Result<Void> assignUserToDepts(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "部门ID列表") @RequestParam List<Long> deptIds,
            @Parameter(description = "主部门ID") @RequestParam(required = false) Long primaryDeptId) {
        boolean success = deptService.assignUserToDepts(userId, deptIds, primaryDeptId);
        if (success) {
            return Result.<Void>success("分配用户部门成功", null);
        } else {
            return Result.error("分配用户部门失败");
        }
    }
    
    @Operation(summary = "从部门移除用户", description = "将用户从指定部门中移除")
    @SaCheckPermission("system:dept:edit")
    @DeleteMapping("/remove-user")
    public Result<Void> removeUserFromDept(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "部门ID") @RequestParam Long deptId) {
        boolean success = deptService.removeUserFromDept(userId, deptId);
        if (success) {
            return Result.<Void>success("从部门移除用户成功", null);
        } else {
            return Result.error("从部门移除用户失败");
        }
    }
    
    @Operation(summary = "设置用户主部门", description = "设置用户的主部门")
    @SaCheckPermission("system:dept:edit")
    @PutMapping("/set-primary")
    public Result<Void> setUserPrimaryDept(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "部门ID") @RequestParam Long deptId) {
        boolean success = deptService.setUserPrimaryDept(userId, deptId);
        if (success) {
            return Result.<Void>success("设置用户主部门成功", null);
        } else {
            return Result.error("设置用户主部门失败");
        }
    }

    @Operation(summary = "批量分配用户到部门", description = "将多个用户批量分配到指定部门")
    @SaCheckPermission("system:dept:edit")
    @PostMapping("/batch-assign-users")
    public Result<String> batchAssignUsersToDept(@RequestBody @Valid BatchDeptUserRequest request) {
        try {
            int successCount = deptPermissionService.batchAssignUsersToDept(
                    request.getDeptId(),
                    request.getUserIds(),
                    request.getIsMain() != null ? request.getIsMain() : false
            );

            String message = String.format("批量分配完成，成功分配 %d 个用户到部门", successCount);
            return Result.success(message, message);
        } catch (Exception e) {
            log.error("批量分配用户到部门失败", e);
            return Result.error("批量分配用户到部门失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量从部门移除用户", description = "将多个用户批量从指定部门中移除")
    @SaCheckPermission("system:dept:edit")
    @PostMapping("/batch-remove-users")
    public Result<String> batchRemoveUsersFromDept(@RequestBody @Valid BatchDeptUserRequest request) {
        try {
            int successCount = deptPermissionService.batchRemoveUsersFromDept(
                    request.getDeptId(),
                    request.getUserIds()
            );

            String message = String.format("批量移除完成，成功从部门移除 %d 个用户", successCount);
            return Result.success(message, message);
        } catch (Exception e) {
            log.error("批量从部门移除用户失败", e);
            return Result.error("批量从部门移除用户失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "获取用户主部门ID", description = "获取用户的主部门ID")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/primary/{userId}")
    public Result<Long> getUserPrimaryDeptId(@Parameter(description = "用户ID") @PathVariable("userId") Long userId) {
        Long deptId = deptService.getUserPrimaryDeptId(userId);
        return Result.success("获取用户主部门ID成功", deptId);
    }
    
    @Operation(summary = "检查部门编码是否存在", description = "检查部门编码是否已存在")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/check-code")
    public Result<Boolean> checkDeptCode(
            @Parameter(description = "部门编码") @RequestParam String deptCode,
            @Parameter(description = "排除的部门ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = deptService.isDeptCodeExists(deptCode, excludeId);
        return Result.success("检查部门编码完成", exists);
    }
    
    @Operation(summary = "检查部门名称是否存在", description = "检查同级部门下部门名称是否已存在")
    @SaCheckPermission("system:dept:query")
    @GetMapping("/check-name")
    public Result<Boolean> checkDeptName(
            @Parameter(description = "部门名称") @RequestParam String deptName,
            @Parameter(description = "父部门ID") @RequestParam Long parentId,
            @Parameter(description = "排除的部门ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = deptService.isDeptNameExists(deptName, parentId, excludeId);
        return Result.success("检查部门名称完成", exists);
    }
    
    @Operation(summary = "初始化系统部门", description = "初始化系统默认部门")
    @SaCheckPermission("system:dept:add")
    @PostMapping("/init")
    public Result<Void> initSystemDepts() {
        boolean success = deptService.initSystemDepts();
        if (success) {
            return Result.<Void>success("初始化系统部门成功", null);
        } else {
            return Result.error("初始化系统部门失败");
        }
    }
}

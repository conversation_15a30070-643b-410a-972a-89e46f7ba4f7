package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.UserSyncService;
import com.jcloud.admin.service.UserSyncDataService;
import com.jcloud.common.dto.*;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户同步控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/system/user/sync")
@RequiredArgsConstructor
@Tag(name = "用户同步管理", description = "用户同步相关接口")
public class UserSyncController {
    
    private final UserSyncService userSyncService;
    private final UserSyncDataService userSyncDataService;

    @Operation(summary = "获取同步预览", description = "获取待同步用户的预览数据，包括新用户、冲突用户、无效用户等信息")
    @SaCheckPermission("system:user:sync")
    @GetMapping("/preview")
    public Result<UserSyncPreviewData> getSyncPreview() {
        try {
            log.info("获取用户同步预览数据");
            UserSyncPreviewData previewData = userSyncService.getSyncPreview();
            return Result.success("获取同步预览成功", previewData);
        } catch (Exception e) {
            log.error("获取同步预览失败", e);
            return Result.error("获取同步预览失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取分页同步预览", description = "分页获取待同步用户列表")
    @SaCheckPermission("system:user:sync")
    @GetMapping("/preview/paged")
    public Result<PageResult<VimUser>> getSyncPreviewPaged(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "用户类型过滤", example = "new") @RequestParam(required = false) String userType) {
        try {
            log.info("获取分页同步预览数据，页码: {}, 页大小: {}, 用户类型: {}", pageNum, pageSize, userType);
            PageResult<VimUser> result = userSyncService.getSyncPreviewPaged(pageNum, pageSize, userType);
            return Result.success("获取分页同步预览成功", result);
        } catch (Exception e) {
            log.error("获取分页同步预览失败", e);
            return Result.error("获取分页同步预览失败: " + e.getMessage());
        }
    }

    @Operation(summary = "验证用户数据", description = "验证用户数据的有效性")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/validate")
    public Result<List<UserValidationError>> validateUsers(@RequestBody List<VimUser> users) {
        try {
            log.info("验证用户数据，用户数量: {}", users.size());
            List<UserValidationError> errors = userSyncDataService.validateUsers(users);
            return Result.success("用户数据验证完成", errors);
        } catch (Exception e) {
            log.error("验证用户数据失败", e);
            return Result.error("验证用户数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检测用户冲突", description = "检测用户与现有数据的冲突情况")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/conflicts")
    public Result<List<UserConflictInfo>> detectConflicts(@RequestBody List<VimUser> users) {
        try {
            log.info("检测用户冲突，用户数量: {}", users.size());
            List<UserConflictInfo> conflicts = userSyncDataService.detectConflicts(users);
            return Result.success("用户冲突检测完成", conflicts);
        } catch (Exception e) {
            log.error("检测用户冲突失败", e);
            return Result.error("检测用户冲突失败: " + e.getMessage());
        }
    }

    @Operation(summary = "执行同步（异步）",
               description = "异步执行用户同步操作，返回任务ID。推荐使用严格模式（STRICT）以获得最佳性能。")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/execute/async")
    public Result<String> executeSyncAsync(@Valid @RequestBody UserSyncExecuteRequest request) {
        try {
            log.info("开始异步执行用户同步，预览ID: {}, 事务模式: {}",
                    request.getPreviewId(), request.getTransactionMode());
            String taskId = userSyncService.executeSyncAsync(request);
            return Result.success("同步任务已创建", taskId);
        } catch (Exception e) {
            log.error("创建异步同步任务失败", e);
            return Result.error("创建同步任务失败: " + e.getMessage());
        }
    }
    // 选择要同步的用户 ---> 分组展示：三列展示：左侧代理列表、中间：主播用户  右侧：普通用户---->

    @Operation(summary = "执行同步（同步）",
               description = "同步执行用户同步操作，直接返回结果。推荐使用严格模式（STRICT）以获得最佳性能。")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/execute/sync")
    public Result<BatchOperationResult<SysUser>> executeSyncSync(@Valid @RequestBody UserSyncExecuteRequest request) {
        try {
            log.info("开始同步执行用户同步，预览ID: {}, 事务模式: {}",
                    request.getPreviewId(), request.getTransactionMode());

            BatchOperationResult<SysUser> result = userSyncService.executeSyncSync(request);

            // 根据结果返回不同的响应
            if (result.isCompleteSuccess()) {
                return Result.success("用户同步完全成功", result);
            } else if (result.isPartialSuccess()) {
                return Result.success("用户同步部分成功", result);
            } else if (result.isCompleteFailure()) {
                return Result.error(500,"用户同步完全失败");
            } else {
                return Result.success("用户同步操作完成", result);
            }
        } catch (Exception e) {
            log.error("执行同步用户失败", e);
            return Result.error("执行同步失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询同步状态", description = "查询异步同步任务的执行状态")
    @SaCheckPermission("system:user:sync")
    @GetMapping("/status/{taskId}")
    public Result<UserSyncTaskStatus> getSyncTaskStatus(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        try {
            log.debug("查询同步任务状态，任务ID: {}", taskId);
            UserSyncTaskStatus status = userSyncService.getSyncTaskStatus(taskId);
            if (status == null) {
                return Result.error("任务不存在或已过期");
            }
            return Result.success("获取任务状态成功", status);
        } catch (Exception e) {
            log.error("查询同步任务状态失败，任务ID: {}", taskId, e);
            return Result.error("查询任务状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "取消同步任务", description = "取消正在执行的异步同步任务")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/cancel/{taskId}")
    public Result<Boolean> cancelSyncTask(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        try {
            log.info("取消同步任务，任务ID: {}", taskId);
            boolean cancelled = userSyncService.cancelSyncTask(taskId);
            if (cancelled) {
                return Result.success("任务取消成功", true);
            } else {
                return Result.error("任务取消失败，任务可能已完成或不存在");
            }
        } catch (Exception e) {
            log.error("取消同步任务失败，任务ID: {}", taskId, e);
            return Result.error("取消任务失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取预览数据", description = "根据预览ID获取预览数据")
    @SaCheckPermission("system:user:sync")
    @GetMapping("/preview/{previewId}")
    public Result<UserSyncPreviewData> getPreviewData(
            @Parameter(description = "预览ID") @PathVariable String previewId) {
        try {
            log.debug("获取预览数据，预览ID: {}", previewId);
            UserSyncPreviewData previewData = userSyncService.getPreviewData(previewId);
            if (previewData == null) {
                return Result.error("预览数据不存在或已过期");
            }
            return Result.success("获取预览数据成功", previewData);
        } catch (Exception e) {
            log.error("获取预览数据失败，预览ID: {}", previewId, e);
            return Result.error("获取预览数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取同步历史", description = "分页获取同步历史记录")
    @SaCheckPermission("system:user:sync")
    @GetMapping("/history")
    public Result<PageResult<UserSyncTaskStatus>> getSyncHistory(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小", example = "10") @RequestParam(defaultValue = "10") int pageSize) {
        try {
            log.info("获取同步历史记录，页码: {}, 页大小: {}", pageNum, pageSize);
            PageResult<UserSyncTaskStatus> result = userSyncService.getSyncHistory(pageNum, pageSize);
            return Result.success("获取同步历史成功", result);
        } catch (Exception e) {
            log.error("获取同步历史失败", e);
            return Result.error("获取同步历史失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "清理过期缓存", description = "清理过期的预览数据和任务状态缓存")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/cleanup")
    public Result<String> cleanupExpiredCaches() {
        try {
            log.info("开始清理过期缓存");
            userSyncService.cleanupExpiredPreviews();
            return Result.success("清理过期缓存成功");
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
            return Result.error("清理过期缓存失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取失败用户详情", description = "获取预览中无效用户的详细信息，用于问题排查")
    @SaCheckPermission("system:user:sync")
    @GetMapping("/preview/{previewId}/invalid-users")
    public Result<List<UserValidationError>> getInvalidUsers(
            @Parameter(description = "预览ID") @PathVariable String previewId) {
        try {
            log.info("获取失败用户详情，预览ID: {}", previewId);
            UserSyncPreviewData previewData = userSyncService.getPreviewData(previewId);
            if (previewData == null) {
                return Result.error("预览数据不存在或已过期");
            }

            List<UserValidationError> invalidUsers = previewData.getInvalidUsers();
            log.info("返回 {} 个无效用户的详细信息", invalidUsers.size());

            return Result.success("获取失败用户详情成功", invalidUsers);
        } catch (Exception e) {
            log.error("获取失败用户详情失败，预览ID: {}", previewId, e);
            return Result.error("获取失败用户详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "分析失败原因", description = "分析无效用户的失败原因统计")
    @SaCheckPermission("system:user:sync")
    @GetMapping("/preview/{previewId}/failure-analysis")
    public Result<Map<String, Object>> analyzeFailures(
            @Parameter(description = "预览ID") @PathVariable String previewId) {
        try {
            log.info("分析失败原因，预览ID: {}", previewId);
            UserSyncPreviewData previewData = userSyncService.getPreviewData(previewId);
            if (previewData == null) {
                return Result.error("预览数据不存在或已过期");
            }

            List<UserValidationError> invalidUsers = previewData.getInvalidUsers();

            // 按错误类型统计
            Map<String, Long> errorTypeStats = invalidUsers.stream()
                    .collect(Collectors.groupingBy(
                            error -> error.getErrorType().getDescription(),
                            Collectors.counting()
                    ));

            // 按错误字段统计
            Map<String, Long> errorFieldStats = invalidUsers.stream()
                    .flatMap(error -> error.getErrorFields().stream())
                    .collect(Collectors.groupingBy(
                            field -> field,
                            Collectors.counting()
                    ));

            // 构建分析结果
            Map<String, Object> analysis = new HashMap<>();
            analysis.put("totalInvalidUsers", invalidUsers.size());
            analysis.put("errorTypeStatistics", errorTypeStats);
            analysis.put("errorFieldStatistics", errorFieldStats);
            analysis.put("analysisTime", System.currentTimeMillis());

            log.info("失败原因分析完成，无效用户: {}, 错误类型: {}, 错误字段: {}",
                    invalidUsers.size(), errorTypeStats.size(), errorFieldStats.size());

            return Result.success("失败原因分析完成", analysis);
        } catch (Exception e) {
            log.error("分析失败原因失败，预览ID: {}", previewId, e);
            return Result.error("分析失败原因失败: " + e.getMessage());
        }
    }
}

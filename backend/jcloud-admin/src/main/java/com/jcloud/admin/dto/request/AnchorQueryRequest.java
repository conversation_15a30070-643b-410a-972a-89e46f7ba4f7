package com.jcloud.admin.dto.request;

import com.jcloud.common.page.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 主播查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "主播查询请求")
public class AnchorQueryRequest extends PageQuery {
    
    /**
     * 主播昵称
     */
    @Schema(description = "主播昵称", example = "主播001")
    private String nickname;
    
    /**
     * 主播状态（1-正常，2-禁用）
     */
    @Schema(description = "主播状态", example = "1")
    private Integer status;
    
    /**
     * 身份类型（2-线上主播，3-线下主播）
     */
    @Schema(description = "身份类型", example = "2")
    private Integer identity;
    
    /**
     * 注册开始时间（时间戳）
     */
    @Schema(description = "注册开始时间", example = "1640995200")
    private Integer registerStartTime;
    
    /**
     * 注册结束时间（时间戳）
     */
    @Schema(description = "注册结束时间", example = "1672531199")
    private Integer registerEndTime;
    
    /**
     * 是否实名认证（0-未实名，1-已实名）
     */
    @Schema(description = "是否实名认证", example = "1")
    private Integer isAuth;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    /**
     * 上级用户名或昵称
     */
    @Schema(description = "上级用户名或昵称", example = "代理001")
    private String managerName;

    /**
     * 可访问的手机号列表（用于数据权限过滤，不对外暴露）
     */
    @Schema(hidden = true)
    private List<String> accessiblePhones;
}

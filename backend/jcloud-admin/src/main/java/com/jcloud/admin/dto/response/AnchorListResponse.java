package com.jcloud.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 主播列表响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "主播列表响应")
public class AnchorListResponse {

    /**
     * 主播ID
     */
    @Schema(description = "主播ID")
    private Integer id;

    /**
     * 主播昵称
     */
    @Schema(description = "主播昵称")
    private String nickname;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String userimage;

    /**
     * 账号状态（1-正常，2-禁用）
     */
    @Schema(description = "账号状态")
    private Integer state;

    /**
     * 身份类型（1-用户，2-线上主播，3-线下主播）
     */
    @Schema(description = "身份类型")
    private Integer identity;

    /**
     * 是否实名认证（0-未实名，1-已实名）
     */
    @Schema(description = "是否实名认证")
    private Integer isauth;

    /**
     * 当前货币余额
     */
    @Schema(description = "当前货币余额")
    private java.math.BigDecimal coin;

    /**
     * 当前钥匙数量
     */
    @Schema(description = "当前钥匙数量")
    private java.math.BigDecimal key;

    /**
     * 下级用户数量
     */
    @Schema(description = "下级用户数量")
    private Integer subUserCount;

    /**
     * 注册时间（时间戳）
     */
    @Schema(description = "注册时间")
    private Integer createTime;

    /**
     * 最后登录时间（时间戳）
     */
    @Schema(description = "最后登录时间")
    private Integer lastLoginTime;

    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    /**
     * 邀请码
     */
    @Schema(description = "邀请码")
    private String inviteCode;

    /**
     * 用户等级
     */
    @Schema(description = "用户等级")
    private Integer level;

    /**
     * 用户经验
     */
    @Schema(description = "用户经验")
    private java.math.BigDecimal exp;

    /**
     * 上级用户id
     */
    @Schema(description = "上级用户id")
    private String managerId;
    /**
     * 上级用户名称
     */
    @Schema(description = "上级用户名称")
    private String managerName;

}

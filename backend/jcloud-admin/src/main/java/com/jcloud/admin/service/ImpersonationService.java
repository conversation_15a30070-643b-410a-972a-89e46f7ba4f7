package com.jcloud.admin.service;

import com.jcloud.common.dto.ImpersonationRequest;
import com.jcloud.common.dto.ImpersonationResponse;
import com.jcloud.common.dto.ImpersonationStatusResponse;

/**
 * 模拟登录服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ImpersonationService {
    
    /**
     * 开始模拟登录
     * 
     * @param request 模拟登录请求
     * @return 模拟登录响应
     */
    ImpersonationResponse startImpersonation(ImpersonationRequest request);
    
    /**
     * 停止模拟登录
     * 
     * @return 模拟登录响应
     */
    ImpersonationResponse stopImpersonation();
    
    /**
     * 获取模拟登录状态
     * 
     * @return 模拟登录状态响应
     */
    ImpersonationStatusResponse getImpersonationStatus();
    
    /**
     * 检查当前是否处于模拟状态
     * 
     * @return 是否处于模拟状态
     */
    boolean isImpersonating();
    
    /**
     * 检查指定用户是否可以被模拟
     * 
     * @param targetUserId 目标用户ID
     * @return 是否可以被模拟
     */
    boolean canImpersonate(Long targetUserId);
}

package com.jcloud.admin.service;

import com.jcloud.admin.dto.request.*;
import com.jcloud.admin.dto.response.*;
import com.jcloud.common.page.PageResult;

/**
 * 运营数据服务接口
 * 提供主播业务数据的查询和监控功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OperationsService {
    
    /**
     * 获取主播列表
     * 支持分页、搜索和筛选功能
     * 
     * @param request 查询请求参数
     * @return 分页的主播列表
     */
    PageResult<AnchorListResponse> getAnchorList(AnchorQueryRequest request);

    /**
     * 获取主播详细统计数据
     * 调用getAnchorStatistics存储过程
     *
     * @param anchorId 主播ID（vim_user表的ID）
     * @param startTime 开始时间（时间戳，可选）
     * @param endTime 结束时间（时间戳，可选）
     * @return 主播统计数据
     */
    AnchorStatsResponse getAnchorStatistics(Integer anchorId, Integer startTime, Integer endTime);

    /**
     * 通过sys_user ID获取主播详细统计数据（简化版）
     * @param sysUserId sys_user表的用户ID（当前登录主播用户的ID）
     * @param startTime 开始时间（时间戳，可选）
     * @param endTime 结束时间（时间戳，可选）
     * @return 主播统计数据
     */
    AnchorStatsResponse getAnchorStatsBySysUserId(Long sysUserId, Integer startTime, Integer endTime);

    /**
     * 获取主播首充统计数据
     * 获取主播下级用户的首充人数和转化率统计
     *
     * @param anchorId 主播ID（vim_user表的ID）
     * @param startTime 开始时间（时间戳，可选）
     * @param endTime 结束时间（时间戳，可选）
     * @return 首充统计数据
     */
    FirstRechargeStatsResponse getFirstRechargeStats(Integer anchorId, Integer startTime, Integer endTime);

    /**
     * 通过sys_user ID获取主播首充统计数据
     * 先将sys_user ID转换为vim_user ID，然后获取首充统计数据
     *
     * @param sysUserId sys_user表的用户ID
     * @param startTime 开始时间（时间戳，可选）
     * @param endTime 结束时间（时间戳，可选）
     * @return 首充统计数据
     */
    FirstRechargeStatsResponse getFirstRechargeStatsBySysUserId(Long sysUserId, Integer startTime, Integer endTime);

    /**
     * 获取主播下级用户列表
     * 支持分页和搜索功能
     * 
     * @param anchorId 主播ID
     * @param request 查询请求参数
     * @return 分页的下级用户列表
     */
    PageResult<SubUserResponse> getSubUsers(Integer anchorId, SubUserQueryRequest request);
    
    /**
     * 获取用户消费详情
     * 查询用户的消费记录，支持时间范围筛选
     * 
     * @param userId 用户ID
     * @param request 查询请求参数
     * @return 分页的消费详情列表
     */
    PageResult<ConsumeDetailResponse> getUserConsumeDetails(Integer userId, ConsumeQueryRequest request);
    
    /**
     * 获取用户充值详情
     * 查询用户的充值记录，支持时间范围筛选
     *
     * @param userId 用户ID
     * @param request 查询请求参数
     * @return 分页的充值详情列表
     */
    PageResult<RechargeDetailResponse> getUserRechargeDetails(Integer userId, RechargeQueryRequest request);

    /**
     * 获取运营统计数据
     * 获取总主播数、活跃主播数、总用户数等关键指标
     *
     * @param startTime 开始时间（时间戳，可选）
     * @param endTime   结束时间（时间戳，可选）
     * @return 运营统计数据
     */
    SubordinateProfit getOperationsStats(Integer startTime, Integer endTime);

    /**
     * 获取用户完整手机号
     *
     * @param userId 用户ID
     * @return 完整的手机号
     */
    String getUserFullPhone(Long userId);
}

package com.jcloud.admin.service;

import com.jcloud.common.dto.UserConflictInfo;
import com.jcloud.common.dto.UserSyncPreviewData;
import com.jcloud.common.dto.UserValidationError;
import com.jcloud.common.entity.VimUser;

import java.util.List;

/**
 * 用户同步数据服务接口
 * 负责用户同步相关的数据获取、验证和冲突检测
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserSyncDataService {
    
    /**
     * 获取预览数据
     * 
     * @param previewId 预览ID
     * @return 预览数据
     */
    UserSyncPreviewData getPreviewData(String previewId);
    
    /**
     * 验证用户数据
     * 
     * @param users 用户列表
     * @return 验证错误列表
     */
    List<UserValidationError> validateUsers(List<VimUser> users);
    
    /**
     * 检测用户冲突
     * 
     * @param users 用户列表
     * @return 冲突信息列表
     */
    List<UserConflictInfo> detectConflicts(List<VimUser> users);
    
    /**
     * 缓存预览数据
     * 
     * @param previewId 预览ID
     * @param previewData 预览数据
     */
    void cachePreviewData(String previewId, UserSyncPreviewData previewData);
    
    /**
     * 清理过期的预览数据
     */
    void cleanupExpiredPreviews();
}

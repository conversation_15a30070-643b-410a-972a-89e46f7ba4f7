package com.jcloud.admin.service;

import com.jcloud.common.dto.*;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.page.PageResult;

import java.util.List;

/**
 * 用户同步服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserSyncService {
    
    /**
     * 获取同步预览数据
     * 
     * @return 同步预览数据
     */
    UserSyncPreviewData getSyncPreview();
    
    /**
     * 获取分页的同步预览数据
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param userType 用户类型过滤（可选）
     * @return 分页的同步预览数据
     */
    PageResult<VimUser> getSyncPreviewPaged(int pageNum, int pageSize, String userType);
    /**
     * 执行用户同步（异步）
     * 
     * @param request 同步执行请求
     * @return 任务ID
     */
    String executeSyncAsync(UserSyncExecuteRequest request);
    
    /**
     * 执行用户同步（同步）
     * 
     * @param request 同步执行请求
     * @return 同步结果
     */
    BatchOperationResult<SysUser> executeSyncSync(UserSyncExecuteRequest request);
    
    /**
     * 获取同步任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    UserSyncTaskStatus getSyncTaskStatus(String taskId);
    
    /**
     * 取消同步任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelSyncTask(String taskId);
    
    /**
     * 获取预览会话数据
     * 
     * @param previewId 预览会话ID
     * @return 预览数据
     */
    UserSyncPreviewData getPreviewData(String previewId);
    
    /**
     * 清理过期的预览会话
     */
    void cleanupExpiredPreviews();
    
    /**
     * 获取同步历史记录
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 同步历史记录
     */
    PageResult<UserSyncTaskStatus> getSyncHistory(int pageNum, int pageSize);
}

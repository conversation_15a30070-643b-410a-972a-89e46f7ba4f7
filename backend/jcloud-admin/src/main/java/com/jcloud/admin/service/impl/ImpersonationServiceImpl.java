package com.jcloud.admin.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jcloud.admin.service.ImpersonationService;
import com.jcloud.admin.service.SysOperLogService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.service.DeptPermissionService;
import com.jcloud.admin.service.SysRoleService;
import com.jcloud.admin.service.SysMenuService;
import com.jcloud.common.dto.ImpersonationRequest;
import com.jcloud.common.dto.ImpersonationResponse;
import com.jcloud.common.dto.ImpersonationStatusResponse;
import com.jcloud.common.entity.ImpersonationContext;
import com.jcloud.common.entity.SysOperLog;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.SysRole;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.enums.BusinessType;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.util.ServletUtils;
import com.jcloud.common.util.TimeUtil;
import com.jcloud.common.constant.CommonConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 模拟登录服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImpersonationServiceImpl implements ImpersonationService {
    
    private final SysUserService userService;
    private final SysOperLogService operLogService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DeptPermissionService deptPermissionService;
    private final SysRoleService roleService;
    private final SysMenuService menuService;
    
    /**
     * Redis Key前缀
     */
    private static final String IMPERSONATION_KEY_PREFIX = "jcloud:impersonation:";
    
    /**
     * 模拟登录最大有效期（小时）- 设置为永久有效，这里设置一个较大值作为保护
     */
    private static final long MAX_IMPERSONATION_HOURS = 24; // 7天
    
    @Override
    public ImpersonationResponse startImpersonation(ImpersonationRequest request) {
        log.info("🚀 开始模拟登录流程: targetUserId={}, reason={}", request.getTargetUserId(), request.getReason());

        // 1. 验证当前用户是否为超级管理员
        Long currentUserId = SecurityUtils.getUserId();
        String currentUsername = SecurityUtils.getUsername();
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin();

        log.info("🔍 权限检查: currentUserId={}, currentUsername={}, isSuperAdmin={}",
                currentUserId, currentUsername, isSuperAdmin);

        if (!isSuperAdmin) {
            log.warn("❌ 非超级管理员尝试模拟登录: userId={}", currentUserId);
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "只有超级管理员才能使用模拟登录功能");
        }
        
        // 2. 检查是否已经处于模拟状态
        if (isImpersonating()) {
            log.warn("用户已处于模拟状态，不能嵌套模拟: adminUserId={}", SecurityUtils.getUserId());
            throw new BusinessException("当前已处于模拟状态，请先退出模拟再进行新的模拟登录");
        }
        
        // 3. 验证目标用户
        Long targetUserId = request.getTargetUserId();
        if (!canImpersonate(targetUserId)) {
            log.warn("目标用户不能被模拟: targetUserId={}", targetUserId);
            throw new BusinessException("目标用户不存在或不允许被模拟");
        }
        
        SysUser targetUser = userService.getById(targetUserId);
        if (targetUser == null) {
            throw new BusinessException("目标用户不存在");
        }
        
        // 4. 获取当前管理员信息
        Long adminUserId = SecurityUtils.getUserId();
        String adminUsername = SecurityUtils.getUsername();
        
        // 5. 生成关联ID用于审计追踪
        String correlationId = IdUtil.simpleUUID();
        
        // 6. 记录开始模拟的审计日志
        recordImpersonationLog("IMPERSONATE_START", adminUserId, adminUsername, 
                              targetUserId, targetUser.getUsername(), request.getReason(), 
                              correlationId, null, 0);
        
        try {
            // 7. 执行sa-token登录切换
            log.info("🔄 执行sa-token登录切换: targetUserId={}", targetUserId);
            StpUtil.login(targetUserId);

            // 8. 获取新生成的token
            String newToken = StpUtil.getTokenValue();
            log.info("✅ sa-token登录切换成功: newToken={}", newToken != null ? newToken.substring(0, Math.min(10, newToken.length())) + "..." : "null");
            
            // 9. 设置目标用户的完整会话信息
            // 设置目标用户的基本信息到Session
            StpUtil.getSession().set("username", targetUser.getUsername());
            StpUtil.getSession().set("nickname", targetUser.getNickname());
            StpUtil.getSession().set("tenantId", targetUser.getTenantId());

            // 🔒 安全修复：模拟登录时不继承目标用户的管理员权限
            // 只有当目标用户确实是超级管理员时才设置为管理员
            // 普通主播用户在模拟登录时不应该获得管理员权限
            boolean targetIsRealAdmin = targetUser.getIsAdmin() != null && targetUser.getIsAdmin() == 1;
            if (targetIsRealAdmin) {
                // 验证目标用户是否真的有超级管理员角色
                List<SysRole> targetUserRoles = roleService.getRolesByUserId(targetUserId);
                boolean hasAdminRole = targetUserRoles.stream()
                        .anyMatch(role -> CommonConstants.SUPER_ADMIN_ROLE.equals(role.getRoleCode()));

                if (hasAdminRole) {
                    StpUtil.getSession().set("isAdmin", 1);
                    log.info("🔒 目标用户是真正的超级管理员，保持管理员权限: targetUserId={}", targetUserId);
                } else {
                    StpUtil.getSession().set("isAdmin", 0);
                    log.warn("⚠️ 目标用户在数据库中标记为管理员但没有管理员角色，设置为普通用户: targetUserId={}", targetUserId);
                }
            } else {
                StpUtil.getSession().set("isAdmin", 0);
                log.info("👤 目标用户是普通用户，设置为非管理员: targetUserId={}", targetUserId);
            }

            // 获取目标用户的数据权限范围
            String targetUserDataScope = getUserDataScope(targetUserId);
            StpUtil.getSession().set("dataScope", targetUserDataScope);
            log.info("✅ 设置目标用户数据权限范围: targetUserId={}, dataScope={}", targetUserId, targetUserDataScope);

            // 验证设置是否成功
            String verifyDataScope = (String) StpUtil.getSession().get("dataScope");
            log.info("🔍 验证数据权限范围设置: 期望={}, 实际={}", targetUserDataScope, verifyDataScope);

            // 设置目标用户的权限和角色信息到sa-token
            setTargetUserPermissions(targetUserId);

            // 获取目标用户的主部门ID
            Long targetUserDeptId = deptPermissionService.getUserPrimaryDeptId(targetUserId);
            if (targetUserDeptId != null) {
                StpUtil.getSession().set("deptId", targetUserDeptId);
                log.info("设置目标用户主部门到Session: targetUserId={}, deptId={}", targetUserId, targetUserDeptId);
            }

            // 在Token-Session中标记模拟状态
            StpUtil.getTokenSession().set("impersonating", true);
            StpUtil.getTokenSession().set("impersonatorId", adminUserId);
            StpUtil.getTokenSession().set("impersonatorUsername", adminUsername);
            StpUtil.getTokenSession().set("reason", request.getReason());
            Long startTimeTimestamp = System.currentTimeMillis() / 1000; // 秒级时间戳
            StpUtil.getTokenSession().set("startTime", startTimeTimestamp);
            StpUtil.getTokenSession().set("correlationId", correlationId);
            
            // 10. 创建模拟上下文并存储到Redis
            ImpersonationContext context = ImpersonationContext.builder()
                    .impersonationToken(newToken)
                    .adminUserId(adminUserId)
                    .adminUsername(adminUsername)
                    .targetUserId(targetUserId)
                    .targetUsername(targetUser.getUsername())
                    .reason(request.getReason())
                    .startTime(startTimeTimestamp)
                    .clientIp(ServletUtils.getClientIP())
                    .userAgent(ServletUtils.getRequest().getHeader("User-Agent"))
                    .tenantId(SecurityUtils.getTenantId())
                    .correlationId(correlationId)
                    .build();
            
            String redisKey = IMPERSONATION_KEY_PREFIX + newToken;
            redisTemplate.opsForValue().set(redisKey, context, MAX_IMPERSONATION_HOURS, TimeUnit.HOURS);
            
            // 11. 构建响应
            ImpersonationResponse response = ImpersonationResponse.builder()
                    .token(newToken)
                    .impersonating(true)
                    .targetUserId(targetUserId)
                    .targetUsername(targetUser.getUsername())
                    .adminUserId(adminUserId)
                    .adminUsername(adminUsername)
                    .startTime(startTimeTimestamp)
                    .reason(request.getReason())
                    .bannerMessage(String.format("您正在以 %s 的身份浏览系统", targetUser.getUsername()))
                    .build();
            
            log.info("模拟登录成功: adminUserId={}, targetUserId={}, correlationId={}", 
                    adminUserId, targetUserId, correlationId);
            
            return response;
            
        } catch (Exception e) {
            // 记录失败的审计日志
            recordImpersonationLog("IMPERSONATE_START", adminUserId, adminUsername, 
                                  targetUserId, targetUser.getUsername(), request.getReason(), 
                                  correlationId, e.getMessage(), 1);
            
            log.error("模拟登录失败: adminUserId={}, targetUserId={}, error={}", 
                     adminUserId, targetUserId, e.getMessage(), e);
            
            throw new BusinessException("模拟登录失败: " + e.getMessage());
        }
    }
    
    @Override
    public ImpersonationResponse stopImpersonation() {
        // 1. 检查当前是否处于模拟状态
        if (!isImpersonating()) {
            throw new BusinessException("当前未处于模拟状态");
        }
        
        // 2. 获取模拟上下文信息
        String currentToken = StpUtil.getTokenValue();
        Object impersonatorId = StpUtil.getTokenSession().get("impersonatorId");
        Object impersonatorUsername = StpUtil.getTokenSession().get("impersonatorUsername");
        Object correlationId = StpUtil.getTokenSession().get("correlationId");
        
        if (impersonatorId == null) {
            throw new BusinessException("无法获取原管理员信息");
        }
        
        Long adminUserId = Long.valueOf(impersonatorId.toString());
        String adminUsername = impersonatorUsername != null ? impersonatorUsername.toString() : "unknown";
        String corrId = correlationId != null ? correlationId.toString() : IdUtil.simpleUUID();
        
        // 3. 获取当前目标用户信息
        Long targetUserId = StpUtil.getLoginIdAsLong();
        String targetUsername = SecurityUtils.getUsername();
        
        try {
            // 4. 注销当前模拟会话
            StpUtil.logout();
            
            // 5. 为原管理员重新生成新的会话token
            StpUtil.login(adminUserId);
            String newAdminToken = StpUtil.getTokenValue();
            
            // 6. 清理Redis中的模拟上下文
            String redisKey = IMPERSONATION_KEY_PREFIX + currentToken;
            redisTemplate.delete(redisKey);
            
            // 7. 记录结束模拟的审计日志
            recordImpersonationLog("IMPERSONATE_STOP", adminUserId, adminUsername, 
                                  targetUserId, targetUsername, "模拟登录结束", 
                                  corrId, null, 0);
            
            // 8. 构建响应
            ImpersonationResponse response = ImpersonationResponse.builder()
                    .token(newAdminToken)
                    .impersonating(false)
                    .adminUserId(adminUserId)
                    .adminUsername(adminUsername)
                    .build();
            
            log.info("模拟登录结束成功: adminUserId={}, targetUserId={}, correlationId={}", 
                    adminUserId, targetUserId, corrId);
            
            return response;
            
        } catch (Exception e) {
            // 记录失败的审计日志
            recordImpersonationLog("IMPERSONATE_STOP", adminUserId, adminUsername, 
                                  targetUserId, targetUsername, "模拟登录结束", 
                                  corrId, e.getMessage(), 1);
            
            log.error("模拟登录结束失败: adminUserId={}, targetUserId={}, error={}", 
                     adminUserId, targetUserId, e.getMessage(), e);
            
            throw new BusinessException("结束模拟登录失败: " + e.getMessage());
        }
    }
    
    @Override
    public ImpersonationStatusResponse getImpersonationStatus() {
        if (!isImpersonating()) {
            return ImpersonationStatusResponse.builder()
                    .impersonating(false)
                    .build();
        }
        
        // 获取模拟状态信息
        Object impersonatorId = StpUtil.getTokenSession().get("impersonatorId");
        Object impersonatorUsername = StpUtil.getTokenSession().get("impersonatorUsername");
        Object reason = StpUtil.getTokenSession().get("reason");
        Object startTime = StpUtil.getTokenSession().get("startTime");
        Object correlationId = StpUtil.getTokenSession().get("correlationId");
        
        Long targetUserId = StpUtil.getLoginIdAsLong();
        String targetUsername = SecurityUtils.getUsername();
        
        return ImpersonationStatusResponse.builder()
                .impersonating(true)
                .targetUserId(targetUserId)
                .targetUsername(targetUsername)
                .adminUserId(impersonatorId != null ? Long.valueOf(impersonatorId.toString()) : null)
                .adminUsername(impersonatorUsername != null ? impersonatorUsername.toString() : null)
                .reason(reason != null ? reason.toString() : null)
                .startTime(startTime != null ? Long.valueOf(startTime.toString()) : null)
                .correlationId(correlationId != null ? correlationId.toString() : null)
                .build();
    }
    
    @Override
    public boolean isImpersonating() {
        try {
            if (!StpUtil.isLogin()) {
                return false;
            }
            
            Object impersonating = StpUtil.getTokenSession().get("impersonating");
            return impersonating != null && Boolean.parseBoolean(impersonating.toString());
        } catch (Exception e) {
            log.debug("检查模拟状态失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean canImpersonate(Long targetUserId) {
        if (targetUserId == null) {
            return true;
        }
        return true;
        /*// 检查目标用户是否存在且状态正常
        SysUser targetUser = userService.getById(targetUserId);
        return targetUser != null && targetUser.getStatus() != 0;*/
    }
    
    /**
     * 设置目标用户的权限和角色信息到sa-token
     */
    private void setTargetUserPermissions(Long targetUserId) {
        try {
            log.info("🔧 开始设置目标用户权限信息: targetUserId={}", targetUserId);

            // 获取目标用户的角色列表
            List<SysRole> userRoles = roleService.getRolesByUserId(targetUserId);
            log.info("📋 目标用户角色列表: targetUserId={}, roles={}", targetUserId,
                    userRoles.stream().map(SysRole::getRoleName).toList());

            // 设置角色到sa-token
            List<String> roleCodes = userRoles.stream()
                    .map(SysRole::getRoleCode)
                    .filter(roleCode -> roleCode != null && !roleCode.trim().isEmpty())
                    .toList();

            for (String roleCode : roleCodes) {
                StpUtil.getRoleList().add(roleCode);
                log.debug("添加角色: {}", roleCode);
            }

            // 获取目标用户的菜单列表（包含权限信息）
            List<SysMenu> userMenus = menuService.getMenusByUserId(targetUserId);
            log.info("🔑 目标用户菜单列表: targetUserId={}, menuCount={}", targetUserId, userMenus.size());

            // 从菜单中提取权限标识并设置到sa-token
            int permissionCount = 0;
            for (SysMenu menu : userMenus) {
                if (menu.getPermissionCode() != null && !menu.getPermissionCode().trim().isEmpty()) {
                    StpUtil.getPermissionList().add(menu.getPermissionCode());
                    log.debug("添加权限: {}", menu.getPermissionCode());
                    permissionCount++;
                }
            }

            log.info("✅ 目标用户权限信息设置完成: targetUserId={}, roleCount={}, permissionCount={}",
                    targetUserId, roleCodes.size(), permissionCount);

        } catch (Exception e) {
            log.error("❌ 设置目标用户权限信息失败: targetUserId={}", targetUserId, e);
        }
    }

    /**
     * 获取用户数据权限范围（基于用户角色的最高权限）
     */
    private String getUserDataScope(Long userId) {
        try {
            List<SysRole> userRoles = roleService.getRolesByUserId(userId);

            // 权限范围优先级：ALL > DEPT_AND_SUB > DEPT > CUSTOM > SELF
            String[] scopePriority = {"ALL", "DEPT_AND_SUB", "DEPT", "CUSTOM", "SELF"};

            for (String scope : scopePriority) {
                for (SysRole role : userRoles) {
                    if (scope.equals(role.getDataScope())) {
                        log.debug("用户数据权限范围: userId={}, dataScope={}, roleName={}",
                                userId, scope, role.getRoleName());
                        return scope;
                    }
                }
            }

            // 默认返回最严格的权限
            return "SELF";
        } catch (Exception e) {
            log.error("获取用户数据权限范围失败: userId={}", userId, e);
            return "SELF";
        }
    }

    /**
     * 记录模拟登录审计日志
     */
    private void recordImpersonationLog(String operation, Long adminUserId, String adminUsername,
                                       Long targetUserId, String targetUsername, String reason,
                                       String correlationId, String errorMsg, Integer status) {
        try {
            SysOperLog operLog = new SysOperLog();
            operLog.setTitle("管理员模拟登录");
            operLog.setBusinessType(BusinessType.IMPERSONATE.getCode()); // 使用模拟登录类型
            operLog.setOperatorType(1); // 后台用户
            operLog.setOperName(adminUsername);
            operLog.setOperUrl(ServletUtils.getRequest().getRequestURI());
            operLog.setOperIp(ServletUtils.getClientIP());
            operLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            operLog.setStatus(status);
            operLog.setOperTime(TimeUtil.now());
            
            // 构建操作参数
            String operParam = String.format("operation=%s, adminUserId=%d, targetUserId=%d, reason=%s, correlationId=%s",
                    operation, adminUserId, targetUserId, reason, correlationId);
            operLog.setOperParam(operParam);
            
            if (StrUtil.isNotBlank(errorMsg)) {
                operLog.setErrorMsg(StrUtil.sub(errorMsg, 0, 2000));
            }
            
            // 异步记录日志
            operLogService.recordOperLog(operLog);
            
        } catch (Exception e) {
            log.error("记录模拟登录审计日志失败: {}", e.getMessage(), e);
        }
    }
}

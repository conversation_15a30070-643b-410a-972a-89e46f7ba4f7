package com.jcloud.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.service.UserSyncDataService;
import com.jcloud.common.dto.UserConflictInfo;
import com.jcloud.common.dto.UserSyncPreviewData;
import com.jcloud.common.dto.UserValidationError;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户同步数据服务实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSyncDataServiceImpl implements UserSyncDataService {
    
    private final SysUserService sysUserService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 内存缓存，用于存储预览数据
     */
    private final Map<String, UserSyncPreviewData> previewCache = new ConcurrentHashMap<>();
    
    /**
     * 缓存键前缀
     */
    private static final String PREVIEW_CACHE_PREFIX = "user_sync:preview:";
    // 30分钟
    private static final long PREVIEW_CACHE_EXPIRE = 30;
    
    @Override
    public UserSyncPreviewData getPreviewData(String previewId) {
        // 先从内存缓存获取
        UserSyncPreviewData previewData = previewCache.get(previewId);
        if (previewData != null) {
            return previewData;
        }

        // 从Redis缓存获取
        try {
            String cacheKey = PREVIEW_CACHE_PREFIX + previewId;
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached instanceof UserSyncPreviewData) {
                previewData = (UserSyncPreviewData) cached;
                // 重新放入内存缓存
                previewCache.put(previewId, previewData);
                return previewData;
            }
        } catch (Exception e) {
            log.warn("从Redis获取预览数据失败，预览ID: {}", previewId, e);
        }

        return null;
    }
    
    @Override
    public List<UserValidationError> validateUsers(List<VimUser> users) {
        List<UserValidationError> errors = new ArrayList<>();

        for (VimUser user : users) {
            List<String> errorFields = new ArrayList<>();
            StringBuilder errorMessage = new StringBuilder();

            // 验证手机号
            if (StrUtil.isBlank(user.getPhone())) {
                errorFields.add("phone");
                errorMessage.append("手机号不能为空; ");
            }
            // 如果有错误，添加到错误列表
            if (!errorFields.isEmpty()) {
                UserValidationError error = UserValidationError.builder()
                        // 设置用户对象
                        .user(user)
                        .userIdentifier(user.getPhone())
                        .errorFields(errorFields)
                        .errorMessage(errorMessage.toString().replaceAll("; $", ""))
                        .errorLevel(UserValidationError.ErrorLevel.ERROR)
                        .build();
                errors.add(error);
            }
        }

        return errors;
    }
    
    @Override
    public List<UserConflictInfo> detectConflicts(List<VimUser> users) {
        List<UserConflictInfo> conflicts = new ArrayList<>();

        try {
            // 批量查询现有用户
            List<String> phones = users.stream()
                    .map(VimUser::getPhone)
                    .collect(Collectors.toList());

            Long tenantId = SecurityUtils.getTenantId();
            Map<String, SysUser> existingUsers = sysUserService.batchQueryExistingUsersByPhone(phones, tenantId);

            // 检测冲突
            for (VimUser user : users) {
                SysUser existingUser = existingUsers.get(user.getPhone());
                if (existingUser != null) {
                    UserConflictInfo conflict = UserConflictInfo.builder()
                            .syncUser(user)
                            .existingUser(existingUser)
                            .conflictType(UserConflictInfo.ConflictType.PHONE_CONFLICT)
                            .conflictFields(List.of("phone"))
                            .conflictDescription("手机号已存在")
                            .suggestedAction("用户已存在，可以选择跳过、覆盖或创建新用户")
                            .autoResolvable(true)
                            .build();
                    conflicts.add(conflict);
                }
            }

        } catch (Exception e) {
            log.error("检测用户冲突失败", e);
        }

        return conflicts;
    }
    
    @Override
    public void cachePreviewData(String previewId, UserSyncPreviewData previewData) {
        try {
            // 内存缓存
            previewCache.put(previewId, previewData);

            // Redis缓存
            String cacheKey = PREVIEW_CACHE_PREFIX + previewId;
            redisTemplate.opsForValue().set(cacheKey, previewData, PREVIEW_CACHE_EXPIRE, TimeUnit.MINUTES);

            log.debug("预览数据已缓存，预览ID: {}", previewId);
        } catch (Exception e) {
            log.warn("缓存预览数据失败，预览ID: {}", previewId, e);
        }
    }
    
    @Override
    public void cleanupExpiredPreviews() {
        // 清理内存缓存中的过期数据
        long now = System.currentTimeMillis();
        previewCache.entrySet().removeIf(entry -> {
            UserSyncPreviewData data = entry.getValue();
            return (now - data.getPreviewTime()) > PREVIEW_CACHE_EXPIRE * 60 * 1000;
        });

        log.debug("清理过期预览缓存完成");
    }
    
    /**
     * 验证手机号格式
     */
    private boolean isValidPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        // 简单的手机号验证：11位数字，以1开头
        return phone.matches("^1[3-9]\\d{9}$");
    }
}

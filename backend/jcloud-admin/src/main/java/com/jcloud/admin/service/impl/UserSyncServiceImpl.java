package com.jcloud.admin.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.service.UserSyncDataService;
import com.jcloud.admin.service.UserSyncService;
import com.jcloud.admin.service.sync.StepContext;
import com.jcloud.admin.service.sync.StepExecutorManager;
import com.jcloud.admin.service.sync.StepResult;
import com.jcloud.admin.service.sync.SyncTaskStatusManager;
import com.jcloud.common.dto.*;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 用户同步服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSyncServiceImpl implements UserSyncService {

    private final SysUserService sysUserService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final StepExecutorManager stepExecutorManager;
    private final SyncTaskStatusManager taskStatusManager;
    private final UserSyncDataService userSyncDataService;


    @Override
    public UserSyncPreviewData getSyncPreview() {
        log.info("开始获取用户同步预览数据");

        try {
            // 1. 查询待同步用户
            List<VimUser> rawUsers = queryUsersFromSource();
            log.info("从数据源查询到 {} 个用户", rawUsers.size());

            if (rawUsers.isEmpty()) {
                return createEmptyPreviewData();
            }

            // 2. 基本数据过滤
            List<VimUser> allUsers = rawUsers.stream()
                    .filter(user -> user.getPhone() != null && !user.getPhone().trim().isEmpty())
                    .collect(Collectors.toList());
            log.info("过滤后剩余 {} 个用户", allUsers.size());

            // 3. 数据验证
            List<UserValidationError> validationErrors = userSyncDataService.validateUsers(allUsers);
            List<VimUser> validUsers = filterValidUsers(allUsers, validationErrors);

            // 4. 冲突检测
            List<UserConflictInfo> conflicts = userSyncDataService.detectConflicts(validUsers);
            Map<String, UserConflictInfo> conflictMap = conflicts.stream()
                    .collect(Collectors.toMap(
                            conflict -> conflict.getSyncUser().getPhone(),
                            conflict -> conflict
                    ));

            // 5. 分类用户（修复：使用正确的字段进行过滤）
            List<VimUser> usersToSync = validUsers.stream()
                    .filter(user -> !conflictMap.containsKey(user.getPhone()))
                    .collect(Collectors.toList());

            // 6. 构建预览数据
            String previewId = IdUtil.simpleUUID();
            UserSyncPreviewData previewData = UserSyncPreviewData.builder()
                    .previewId(previewId)
                    .totalCount(allUsers.size())
                    .newUserCount(usersToSync.size())
                    .existingUserCount(conflicts.size())
                    .invalidUserCount(validationErrors.size())
                    .usersToSync(usersToSync)
                    .existingUsers(conflictMap)
                    .invalidUsers(validationErrors)
                    .previewTime(TimeUtil.now())
                    .sourceInfo(createSourceInfo())
                    .build();

            // 6. 缓存预览数据
            userSyncDataService.cachePreviewData(previewId, previewData);

            log.info("用户同步预览数据生成完成，预览ID: {}, 总数: {}, 新用户: {}, 已存在: {}, 无效: {}",
                    previewId, previewData.getTotalCount(), previewData.getNewUserCount(),
                    previewData.getExistingUserCount(), previewData.getInvalidUserCount());
            return previewData;
        } catch (Exception e) {
            log.error("获取用户同步预览数据失败", e);
            throw new RuntimeException("获取同步预览数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PageResult<VimUser> getSyncPreviewPaged(int pageNum, int pageSize, String userType) {
        // 获取完整预览数据
        UserSyncPreviewData previewData = getSyncPreview();

        List<VimUser> allUsers = new ArrayList<>();

        // 根据用户类型过滤
        if (StrUtil.isBlank(userType) || "all".equals(userType)) {
            allUsers.addAll(previewData.getUsersToSync());
        } else if ("new".equals(userType)) {
            allUsers.addAll(previewData.getUsersToSync());
        } else if ("existing".equals(userType)) {
            allUsers.addAll(previewData.getExistingUsers().values().stream()
                    .map(UserConflictInfo::getSyncUser)
                    .toList());
        } else if ("invalid".equals(userType)) {
            allUsers.addAll(previewData.getInvalidUsers().stream()
                    .map(UserValidationError::getUser)
                    .toList());
        }

        // 分页处理
        int total = allUsers.size();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<VimUser> pageData = start < total ? allUsers.subList(start, end) : new ArrayList<>();

        return PageResult.<VimUser>builder()
                .records(pageData)
                .total((long) total)
                .totalPages(pageNum)
                .pageSize(pageSize)
                .pageNum((total + pageSize - 1) / pageSize)
                .build();
    }


    /**
     * 过滤有效用户
     */
    private List<VimUser> filterValidUsers(List<VimUser> allUsers, List<UserValidationError> errors) {
        Set<String> invalidPhones = errors.stream()
                .map(error -> error.getUser().getPhone())
                .collect(Collectors.toSet());

        return allUsers.stream()
                .filter(user -> !invalidPhones.contains(user.getPhone()))
                .collect(Collectors.toList());
    }

    /**
     * 查询数据源用户
     */
    private List<VimUser> queryUsersFromSource() {
        try {
            return sysUserService.queryUsersForSync();
        } catch (Exception e) {
            log.error("查询数据源用户失败", e);
            return new ArrayList<>();
        }
    }


    /**
     * 创建空的预览数据
     */
    private UserSyncPreviewData createEmptyPreviewData() {
        return UserSyncPreviewData.builder()
                .previewId(IdUtil.simpleUUID())
                .totalCount(0)
                .newUserCount(0)
                .existingUserCount(0)
                .invalidUserCount(0)
                .usersToSync(new ArrayList<>())
                .existingUsers(new HashMap<>())
                .invalidUsers(new ArrayList<>())
                .previewTime(TimeUtil.now())
                .sourceInfo(createSourceInfo())
                .build();
    }

    /**
     * 创建数据源信息
     */
    private UserSyncPreviewData.DataSourceInfo createSourceInfo() {
        return UserSyncPreviewData.DataSourceInfo.builder()
                .sourceTable("vim_user")
                .queryTime(TimeUtil.now())
                .queryCondition("identity IN (3, 4)")
                .dataVersion(String.valueOf(System.currentTimeMillis()))
                .build();
    }


    @Override
    public String executeSyncAsync(UserSyncExecuteRequest request) {
        String taskId = IdUtil.simpleUUID();

        try {
            // 快速验证请求参数
            if (request.getPreviewId() == null || request.getPreviewId().trim().isEmpty()) {
                throw new IllegalArgumentException("预览ID不能为空");
            }

            // 创建任务状态（优化：减少不必要的字段初始化）
            UserSyncTaskStatus taskStatus = UserSyncTaskStatus.builder()
                    .taskId(taskId)
                    .status(UserSyncTaskStatus.TaskStatus.PENDING)
                    .currentStep(UserSyncTaskStatus.SyncStep.FETCH_AND_VALIDATE)
                    .totalSteps(UserSyncTaskStatus.SyncStep.getTotalSteps())
                    .currentStepIndex(0)
                    .progressPercentage(0)
                    .startTime(System.currentTimeMillis())
                    .currentMessage("任务已创建，等待执行")
                    .stepHistory(new ArrayList<>())
                    .stepDetails(UserSyncTaskStatus.StepDetails.builder()
                            .totalUsers(0)
                            .processedUsers(0)
                            .successUsers(0)
                            .failedUsers(0)
                            .skippedUsers(0)
                            .currentBatch(0)
                            .totalBatches(0)
                            .build())
                    .build();

            // 使用新的状态管理器保存任务状态
            taskStatusManager.saveTaskStatus(taskId, taskStatus);

            // 异步执行同步任务（优化：使用CompletableFuture确保真正异步）
            CompletableFuture.runAsync(() -> executeAsyncTask(taskId, request))
                    .exceptionally(throwable -> {
                        log.error("异步任务启动失败，任务ID: {}", taskId, throwable);
                        taskStatusManager.markTaskFailed(taskId, "任务启动失败: " + throwable.getMessage(),
                                throwable.toString());
                        return null;
                    });

            log.info("异步同步任务已创建并启动，任务ID: {}", taskId);
            return taskId;

        } catch (Exception e) {
            log.error("创建异步同步任务失败", e);
            throw new RuntimeException("创建同步任务失败: " + e.getMessage(), e);
        }
    }

    @Async
    public void executeAsyncTask(String taskId, UserSyncExecuteRequest request) {
        UserSyncTaskStatus taskStatus = taskStatusManager.getTaskStatus(taskId);
        if (taskStatus == null) {
            log.error("任务状态不存在，任务ID: {}", taskId);
            return;
        }

        try {
            // 更新任务状态为运行中
            taskStatusManager.updateTaskStatus(taskId, UserSyncTaskStatus.TaskStatus.RUNNING,
                    UserSyncTaskStatus.SyncStep.FETCH_AND_VALIDATE, "开始执行同步任务");

            // 创建步骤执行上下文
            StepContext context = StepContext.builder()
                    .taskId(taskId)
                    .request(request)
                    .taskStatus(taskStatus)
                    .tenantId(SecurityUtils.getTenantId())
                    .currentUserId(SecurityUtils.getUserId())
                    .batchSize(request.getBatchSize() != null ? request.getBatchSize() : 100)
                    .build();

            // 按顺序执行所有步骤
            UserSyncTaskStatus.SyncStep[] steps = stepExecutorManager.getExecutionOrder();
            Object finalResult = null;

            for (UserSyncTaskStatus.SyncStep step : steps) {
                try {
                    log.info("开始执行步骤: {}, 任务ID: {}", step.getDescription(), taskId);

                    // 更新当前步骤状态
                    taskStatusManager.updateTaskStatus(taskId, UserSyncTaskStatus.TaskStatus.RUNNING,
                            step, "正在执行: " + step.getDescription());

                    // 执行步骤
                    StepResult<?> stepResult = stepExecutorManager.executeStep(step, context);

                    if (!stepResult.isSuccess()) {
                        throw new RuntimeException("步骤执行失败: " + stepResult.getErrorMessage());
                    }

                    // 如果是最后一步，保存最终结果
                    if (step == UserSyncTaskStatus.SyncStep.COMPLETE_CLEANUP) {
                        finalResult = context.getSharedData("finalSyncResult", Object.class);
                    }

                    log.info("步骤执行成功: {}, 任务ID: {}, 耗时: {}ms",
                            step.getDescription(), taskId, stepResult.getDuration());

                } catch (Exception stepException) {
                    log.error("步骤执行失败: {}, 任务ID: {}", step.getDescription(), taskId, stepException);
                    throw new RuntimeException("步骤 " + step.getDescription() + " 执行失败: " + stepException.getMessage(), stepException);
                }
            }

            // 标记任务完成
            taskStatusManager.markTaskCompleted(taskId, finalResult);

            log.info("异步同步任务执行完成，任务ID: {}", taskId);

        } catch (Exception e) {
            log.error("异步同步任务执行失败，任务ID: {}", taskId, e);

            // 标记任务失败
            taskStatusManager.markTaskFailed(taskId, e.getMessage(), e.toString());
        }
    }

    @Override
    public BatchOperationResult<SysUser> executeSyncSync(UserSyncExecuteRequest request) {
        log.info("开始执行同步用户，预览ID: {}, 事务模式: {}", request.getPreviewId(), request.getTransactionMode());

        try {
            // 1. 获取预览数据
            UserSyncPreviewData previewData = getPreviewData(request.getPreviewId());
            if (previewData == null) {
                throw new RuntimeException("预览数据不存在或已过期，预览ID: " + request.getPreviewId());
            }

            // 2. 确定要同步的用户
            List<VimUser> usersToSync = determineUsersToSync(previewData, request);

            if (usersToSync.isEmpty()) {
                return BatchOperationResult.<SysUser>builder()
                        .totalCount(0)
                        .successCount(0)
                        .failureCount(0)
                        .build();
            }

            // 3. 执行批量保存
            BatchOperationResult<SysUser> result = sysUserService.batchSaveUsersWithRoles(
                    usersToSync, request.getTransactionMode());

            log.info("11同步用户执行完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}",
                    result.getTotalCount(), result.getSuccessCount(),
                    result.getFailureCount(), result.getSkipCount());

            return result;

        } catch (Exception e) {
            log.error("执行同步用户失败", e);
            throw new RuntimeException("执行同步失败: " + e.getMessage(), e);
        }
    }

    @Override
    public UserSyncTaskStatus getSyncTaskStatus(String taskId) {
        return taskStatusManager.getTaskStatus(taskId);
    }

    @Override
    public boolean cancelSyncTask(String taskId) {
        UserSyncTaskStatus taskStatus = taskStatusManager.getTaskStatus(taskId);
        if (taskStatus == null) {
            return false;
        }

        if (taskStatus.getStatus() == UserSyncTaskStatus.TaskStatus.RUNNING) {
            taskStatusManager.updateTaskStatus(taskId, UserSyncTaskStatus.TaskStatus.CANCELLED,
                    taskStatus.getCurrentStep(), "任务已被用户取消");
            return true;
        }

        return false;
    }

    @Override
    public UserSyncPreviewData getPreviewData(String previewId) {
        return userSyncDataService.getPreviewData(previewId);
    }

    @Override
    public void cleanupExpiredPreviews() {
        userSyncDataService.cleanupExpiredPreviews();
    }

    @Override
    public PageResult<UserSyncTaskStatus> getSyncHistory(int pageNum, int pageSize) {
        // 这里应该从数据库查询历史记录
        // 暂时返回空结果
        return PageResult.<UserSyncTaskStatus>builder()
                .records(new ArrayList<>())
                .total(0L)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalPages(0)
                .build();
    }

    /**
     * 确定要同步的用户列表
     */
    private List<VimUser> determineUsersToSync(UserSyncPreviewData previewData, UserSyncExecuteRequest request) {
        List<VimUser> usersToSync = previewData.getUsersToSync();

        // 如果指定了特定用户，则过滤
        if (request.getSelectedUserPhones() != null && !request.getSelectedUserPhones().isEmpty()) {
            Set<String> selectedPhones = new HashSet<>(request.getSelectedUserPhones());
            usersToSync = usersToSync.stream()
                    .filter(user -> selectedPhones.contains(user.getPhone()))
                    .collect(Collectors.toList());
        }

        return usersToSync;
    }


    /**
     * 构建详细错误信息
     */
    private String buildDetailMessage(VimUser user, List<String> errorFields) {
        return "用户数据: " +
                "手机号=[" + user.getPhone() + "], " +
                "用户名=[" + user.getUsername() + "], " +
                "身份=[" + user.getIdentity() + "], " +
                "错误字段: " + String.join(", ", errorFields);
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        if (StrUtil.isBlank(email)) {
            return false;
        }
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }

    /**
     * 验证手机号格式
     */
    private boolean isValidPhoneNumber(String phone) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        return phone.matches("^1[3-9]\\d{9}$");
    }
}

package com.jcloud.admin.service.sync;

import com.jcloud.common.dto.UserSyncExecuteRequest;
import com.jcloud.common.dto.UserSyncTaskStatus;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 步骤执行上下文
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StepContext {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 同步执行请求
     */
    private UserSyncExecuteRequest request;
    
    /**
     * 任务状态
     */
    private UserSyncTaskStatus taskStatus;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 当前用户ID
     */
    private Long currentUserId;
    
    /**
     * 步骤间共享数据
     */
    @Builder.Default
    private Map<String, Object> sharedData = new ConcurrentHashMap<>();
    
    /**
     * 步骤执行开始时间
     */
    private Long stepStartTime;
    
    /**
     * 批次大小
     */
    @Builder.Default
    private int batchSize = 100;

    /**
     * 最大批次大小
     */
    @Builder.Default
    private int maxBatchSize = 500;

    /**
     * 最小批次大小
     */
    @Builder.Default
    private int minBatchSize = 10;
    
    /**
     * 获取共享数据
     * 
     * @param key 数据键
     * @param clazz 数据类型
     * @return 数据值
     */
    @SuppressWarnings("unchecked")
    public <T> T getSharedData(String key, Class<T> clazz) {
        Object value = sharedData.get(key);
        if (clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 设置共享数据
     * 
     * @param key 数据键
     * @param value 数据值
     */
    public void setSharedData(String key, Object value) {
        sharedData.put(key, value);
    }
    
    /**
     * 移除共享数据
     * 
     * @param key 数据键
     */
    public void removeSharedData(String key) {
        sharedData.remove(key);
    }
    
    /**
     * 检查是否存在共享数据
     * 
     * @param key 数据键
     * @return 是否存在
     */
    public boolean hasSharedData(String key) {
        return sharedData.containsKey(key);
    }
    
    /**
     * 清空共享数据
     */
    public void clearSharedData() {
        sharedData.clear();
    }
}

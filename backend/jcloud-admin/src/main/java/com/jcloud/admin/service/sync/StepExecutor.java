package com.jcloud.admin.service.sync;

import com.jcloud.common.dto.UserSyncTaskStatus;

/**
 * 同步步骤执行器接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface StepExecutor<T> {
    
    /**
     * 执行步骤
     * 
     * @param context 执行上下文
     * @return 步骤执行结果
     * @throws Exception 执行异常
     */
    StepResult<T> execute(StepContext context) throws Exception;
    
    /**
     * 获取步骤类型
     * 
     * @return 步骤类型
     */
    UserSyncTaskStatus.SyncStep getStepType();
    
    /**
     * 获取步骤名称
     * 
     * @return 步骤名称
     */
    default String getStepName() {
        return getStepType().getDescription();
    }
    
    /**
     * 是否支持重试
     * 
     * @return 是否支持重试
     */
    default boolean isRetryable() {
        return true;
    }
    
    /**
     * 获取最大重试次数
     * 
     * @return 最大重试次数
     */
    default int getMaxRetries() {
        return 3;
    }
    
    /**
     * 步骤执行前的准备工作
     * 
     * @param context 执行上下文
     */
    default void beforeExecute(StepContext context) {
        // 默认空实现
    }
    
    /**
     * 步骤执行后的清理工作
     * 
     * @param context 执行上下文
     * @param result 执行结果
     */
    default void afterExecute(StepContext context, StepResult<T> result) {
        // 默认空实现
    }
    
    /**
     * 步骤执行失败时的处理
     * 
     * @param context 执行上下文
     * @param exception 异常信息
     */
    default void onError(StepContext context, Exception exception) {
        // 默认空实现
    }
}

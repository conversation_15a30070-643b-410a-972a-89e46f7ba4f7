package com.jcloud.admin.service.sync;

import com.jcloud.admin.service.sync.steps.*;
import com.jcloud.common.dto.UserSyncTaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 步骤执行器管理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StepExecutorManager {
    
    private final FetchAndValidateStepExecutor fetchAndValidateStepExecutor;
    private final PreprocessUsersStepExecutor preprocessUsersStepExecutor;
    private final InsertUsersStepExecutor insertUsersStepExecutor;
    private final AssignRolesStepExecutor assignRolesStepExecutor;
    private final ProcessAgentDeptsStepExecutor processAgentDeptsStepExecutor;
    private final CompleteCleanupStepExecutor completeCleanupStepExecutor;
    
    private final Map<UserSyncTaskStatus.SyncStep, StepExecutor<?>> executorMap = new HashMap<>();
    
    /**
     * 初始化执行器映射
     */
    public void init() {
        executorMap.put(UserSyncTaskStatus.SyncStep.FETCH_AND_VALIDATE, fetchAndValidateStepExecutor);
        executorMap.put(UserSyncTaskStatus.SyncStep.PREPROCESS_USERS, preprocessUsersStepExecutor);
        executorMap.put(UserSyncTaskStatus.SyncStep.INSERT_USERS, insertUsersStepExecutor);
        executorMap.put(UserSyncTaskStatus.SyncStep.ASSIGN_ROLES, assignRolesStepExecutor);
        executorMap.put(UserSyncTaskStatus.SyncStep.PROCESS_AGENT_DEPTS, processAgentDeptsStepExecutor);
        executorMap.put(UserSyncTaskStatus.SyncStep.COMPLETE_CLEANUP, completeCleanupStepExecutor);
        
        log.info("步骤执行器管理器初始化完成，注册了 {} 个执行器", executorMap.size());
    }
    
    /**
     * 获取步骤执行器
     * 
     * @param step 步骤类型
     * @return 步骤执行器
     */
    @SuppressWarnings("unchecked")
    public <T> StepExecutor<T> getExecutor(UserSyncTaskStatus.SyncStep step) {
        if (executorMap.isEmpty()) {
            init();
        }
        
        StepExecutor<?> executor = executorMap.get(step);
        if (executor == null) {
            throw new IllegalArgumentException("未找到步骤执行器: " + step);
        }
        
        return (StepExecutor<T>) executor;
    }
    
    /**
     * 执行步骤
     *
     * @param step 步骤类型
     * @param context 执行上下文
     * @return 执行结果
     */
    public StepResult<?> executeStep(UserSyncTaskStatus.SyncStep step, StepContext context) {
        try {
            StepExecutor<Object> executor = getExecutor(step);
            
            log.info("开始执行步骤: {}, 任务ID: {}", step.getDescription(), context.getTaskId());
            
            // 设置步骤开始时间
            context.setStepStartTime(System.currentTimeMillis());
            
            // 执行前置处理
            executor.beforeExecute(context);
            
            // 执行步骤
            StepResult<Object> result = executor.execute(context);

            // 执行后置处理
            executor.afterExecute(context, result);
            
            if (result.isSuccess()) {
                log.info("步骤执行成功: {}, 任务ID: {}, 耗时: {}ms", 
                    step.getDescription(), context.getTaskId(), result.getDuration());
            } else {
                log.error("步骤执行失败: {}, 任务ID: {}, 错误: {}", 
                    step.getDescription(), context.getTaskId(), result.getErrorMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("步骤执行异常: {}, 任务ID: {}", step.getDescription(), context.getTaskId(), e);
            
            // 调用错误处理
            try {
                StepExecutor<?> executor = getExecutor(step);
                executor.onError(context, e);
            } catch (Exception errorHandlingException) {
                log.error("步骤错误处理失败: {}", step.getDescription(), errorHandlingException);
            }
            
            return StepResult.failure("步骤执行异常: " + e.getMessage(), e.toString());
        }
    }
    
    /**
     * 获取所有步骤的执行顺序
     * 
     * @return 步骤列表
     */
    public UserSyncTaskStatus.SyncStep[] getExecutionOrder() {
        return UserSyncTaskStatus.SyncStep.values();
    }
}

package com.jcloud.admin.service.sync;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * 步骤执行结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StepResult<T> {
    
    /**
     * 执行是否成功
     */
    private boolean success;
    
    /**
     * 结果数据
     */
    private T data;
    
    /**
     * 处理总数
     */
    private int totalCount;
    
    /**
     * 成功数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int failureCount;
    
    /**
     * 跳过数量
     */
    private int skipCount;
    
    /**
     * 执行耗时（毫秒）
     */
    private long duration;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误详情
     */
    private String errorDetail;
    
    /**
     * 警告信息列表
     */
    @Builder.Default
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 步骤详细信息
     */
    private StepDetailInfo detailInfo;
    
    /**
     * 创建成功结果
     * 
     * @param data 结果数据
     * @param message 消息
     * @return 成功结果
     */
    public static <T> StepResult<T> success(T data, String message) {
        return StepResult.<T>builder()
                .success(true)
                .data(data)
                .message(message)
                .build();
    }
    
    /**
     * 创建成功结果（带统计信息）
     * 
     * @param data 结果数据
     * @param message 消息
     * @param totalCount 总数
     * @param successCount 成功数
     * @return 成功结果
     */
    public static <T> StepResult<T> success(T data, String message, int totalCount, int successCount) {
        return StepResult.<T>builder()
                .success(true)
                .data(data)
                .message(message)
                .totalCount(totalCount)
                .successCount(successCount)
                .build();
    }
    
    /**
     * 创建失败结果
     * 
     * @param errorMessage 错误消息
     * @return 失败结果
     */
    public static <T> StepResult<T> failure(String errorMessage) {
        return StepResult.<T>builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * 创建失败结果（带详细信息）
     * 
     * @param errorMessage 错误消息
     * @param errorDetail 错误详情
     * @return 失败结果
     */
    public static <T> StepResult<T> failure(String errorMessage, String errorDetail) {
        return StepResult.<T>builder()
                .success(false)
                .errorMessage(errorMessage)
                .errorDetail(errorDetail)
                .build();
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        if (warnings == null) {
            warnings = new ArrayList<>();
        }
        warnings.add(warning);
    }
    
    /**
     * 步骤详细信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StepDetailInfo {
        
        /**
         * 处理的批次数
         */
        private int batchCount;
        
        /**
         * 平均每批处理时间（毫秒）
         */
        private long avgBatchTime;
        
        /**
         * 最大批处理时间（毫秒）
         */
        private long maxBatchTime;
        
        /**
         * 最小批处理时间（毫秒）
         */
        private long minBatchTime;
        
        /**
         * 数据库操作次数
         */
        private int dbOperationCount;
        
        /**
         * 缓存操作次数
         */
        private int cacheOperationCount;
        
        /**
         * 额外信息
         */
        private String additionalInfo;
    }
}

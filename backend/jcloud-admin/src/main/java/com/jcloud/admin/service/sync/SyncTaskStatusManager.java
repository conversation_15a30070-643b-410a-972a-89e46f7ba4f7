package com.jcloud.admin.service.sync;

import com.jcloud.common.dto.UserSyncTaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 同步任务状态管理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SyncTaskStatusManager {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 缓存键前缀
    private static final String TASK_STATUS_PREFIX = "user_sync:task:";
    private static final String TASK_DATA_PREFIX = "user_sync:data:";
    
    // 缓存过期时间
    private static final long TASK_STATUS_EXPIRE_HOURS = 2; // 2小时
    private static final long TASK_DATA_EXPIRE_HOURS = 1;   // 1小时
    
    /**
     * 保存任务状态
     * 
     * @param taskId 任务ID
     * @param taskStatus 任务状态
     */
    public void saveTaskStatus(String taskId, UserSyncTaskStatus taskStatus) {
        try {
            String key = TASK_STATUS_PREFIX + taskId;
            redisTemplate.opsForValue().set(key, taskStatus, TASK_STATUS_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("保存任务状态成功: taskId={}", taskId);
        } catch (Exception e) {
            log.error("保存任务状态失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 获取任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    public UserSyncTaskStatus getTaskStatus(String taskId) {
        try {
            String key = TASK_STATUS_PREFIX + taskId;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached instanceof UserSyncTaskStatus) {
                return (UserSyncTaskStatus) cached;
            }
        } catch (Exception e) {
            log.error("获取任务状态失败: taskId={}", taskId, e);
        }
        return null;
    }
    
    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 任务状态
     * @param step 当前步骤
     * @param message 状态消息
     */
    public void updateTaskStatus(String taskId, UserSyncTaskStatus.TaskStatus status,
                                UserSyncTaskStatus.SyncStep step, String message) {
        try {
            UserSyncTaskStatus taskStatus = getTaskStatus(taskId);
            if (taskStatus != null) {
                taskStatus.setStatus(status);
                taskStatus.setCurrentStep(step);
                taskStatus.setCurrentMessage(message);
                
                // 更新进度
                taskStatus.setProgressPercentage(taskStatus.calculateProgressPercentage());
                
                // 添加步骤记录
                UserSyncTaskStatus.StepRecord stepRecord = UserSyncTaskStatus.StepRecord.builder()
                    .step(step)
                    .status(status)
                    .startTime(System.currentTimeMillis())
                    .message(message)
                    .build();
                
                if (taskStatus.getStepHistory() != null) {
                    taskStatus.getStepHistory().add(stepRecord);
                }
                
                saveTaskStatus(taskId, taskStatus);
                log.debug("更新任务状态成功: taskId={}, status={}, step={}", taskId, status, step);
            }
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 更新步骤详细信息
     * 
     * @param taskId 任务ID
     * @param stepDetails 步骤详细信息
     */
    public void updateStepDetails(String taskId, UserSyncTaskStatus.StepDetails stepDetails) {
        try {
            UserSyncTaskStatus taskStatus = getTaskStatus(taskId);
            if (taskStatus != null) {
                taskStatus.setStepDetails(stepDetails);
                taskStatus.setProgressPercentage(taskStatus.calculateProgressPercentage());
                saveTaskStatus(taskId, taskStatus);
                log.debug("更新步骤详细信息成功: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("更新步骤详细信息失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 标记任务完成
     *
     * @param taskId 任务ID
     * @param syncResult 同步结果
     */
    @SuppressWarnings("unchecked")
    public void markTaskCompleted(String taskId, Object syncResult) {
        try {
            UserSyncTaskStatus taskStatus = getTaskStatus(taskId);
            if (taskStatus != null) {
                taskStatus.setStatus(UserSyncTaskStatus.TaskStatus.COMPLETED);
                taskStatus.setCurrentStep(UserSyncTaskStatus.SyncStep.COMPLETE_CLEANUP);
                taskStatus.setProgressPercentage(100);
                taskStatus.setEndTime(System.currentTimeMillis());
                taskStatus.setDuration(taskStatus.getEndTime() - taskStatus.getStartTime());
                taskStatus.setSyncResult((com.jcloud.common.dto.BatchOperationResult<com.jcloud.common.entity.SysUser>) syncResult);
                taskStatus.setCurrentMessage("同步任务执行完成");

                // 修复：更新stepDetails，确保前端能正确显示统计数据
                if (syncResult instanceof com.jcloud.common.dto.BatchOperationResult) {
                    com.jcloud.common.dto.BatchOperationResult<?> result =
                            (com.jcloud.common.dto.BatchOperationResult<?>) syncResult;

                    UserSyncTaskStatus.StepDetails stepDetails = taskStatus.getStepDetails();
                    if (stepDetails == null) {
                        stepDetails = UserSyncTaskStatus.StepDetails.builder().build();
                    }

                    // 更新最终统计数据
                    stepDetails.setTotalUsers(result.getTotalCount());
                    stepDetails.setProcessedUsers(result.getTotalCount());
                    stepDetails.setSuccessUsers(result.getSuccessCount());
                    stepDetails.setFailedUsers(result.getFailureCount());
                    stepDetails.setSkippedUsers(result.getSkipCount());

                    taskStatus.setStepDetails(stepDetails);

                    log.info("任务完成统计: 总数={}, 成功={}, 失败={}, 跳过={}",
                            result.getTotalCount(), result.getSuccessCount(),
                            result.getFailureCount(), result.getSkipCount());
                }

                saveTaskStatus(taskId, taskStatus);
                log.info("任务标记为完成: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("标记任务完成失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 标记任务失败
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     * @param errorDetail 错误详情
     */
    public void markTaskFailed(String taskId, String errorMessage, String errorDetail) {
        try {
            UserSyncTaskStatus taskStatus = getTaskStatus(taskId);
            if (taskStatus != null) {
                taskStatus.setStatus(UserSyncTaskStatus.TaskStatus.FAILED);
                taskStatus.setProgressPercentage(0); // 失败时重置进度
                taskStatus.setEndTime(System.currentTimeMillis());
                taskStatus.setDuration(taskStatus.getEndTime() - taskStatus.getStartTime());
                taskStatus.setErrorMessage(errorMessage);
                taskStatus.setErrorDetail(errorDetail);
                taskStatus.setCurrentMessage("同步任务执行失败: " + errorMessage);
                
                saveTaskStatus(taskId, taskStatus);
                log.error("任务标记为失败: taskId={}, error={}", taskId, errorMessage);
            }
        } catch (Exception e) {
            log.error("标记任务失败失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 保存步骤数据
     * 
     * @param taskId 任务ID
     * @param stepKey 步骤数据键
     * @param data 数据
     */
    public void saveStepData(String taskId, String stepKey, Object data) {
        try {
            String key = TASK_DATA_PREFIX + taskId + ":" + stepKey;
            redisTemplate.opsForValue().set(key, data, TASK_DATA_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("保存步骤数据成功: taskId={}, stepKey={}", taskId, stepKey);
        } catch (Exception e) {
            log.error("保存步骤数据失败: taskId={}, stepKey={}", taskId, stepKey, e);
        }
    }
    
    /**
     * 获取步骤数据
     * 
     * @param taskId 任务ID
     * @param stepKey 步骤数据键
     * @param clazz 数据类型
     * @return 数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getStepData(String taskId, String stepKey, Class<T> clazz) {
        try {
            String key = TASK_DATA_PREFIX + taskId + ":" + stepKey;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached != null && clazz.isInstance(cached)) {
                return (T) cached;
            }
        } catch (Exception e) {
            log.error("获取步骤数据失败: taskId={}, stepKey={}", taskId, stepKey, e);
        }
        return null;
    }
    
    /**
     * 删除任务相关数据
     * 
     * @param taskId 任务ID
     */
    public void deleteTaskData(String taskId) {
        try {
            // 删除任务状态
            String statusKey = TASK_STATUS_PREFIX + taskId;
            redisTemplate.delete(statusKey);
            
            // 删除步骤数据（使用模式匹配）
            String dataPattern = TASK_DATA_PREFIX + taskId + ":*";
            redisTemplate.delete(redisTemplate.keys(dataPattern));
            
            log.info("删除任务数据成功: taskId={}", taskId);
        } catch (Exception e) {
            log.error("删除任务数据失败: taskId={}", taskId, e);
        }
    }
    
    /**
     * 检查任务是否存在
     * 
     * @param taskId 任务ID
     * @return 是否存在
     */
    public boolean taskExists(String taskId) {
        try {
            String key = TASK_STATUS_PREFIX + taskId;
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查任务是否存在失败: taskId={}", taskId, e);
            return false;
        }
    }
}

package com.jcloud.admin.service.sync.steps;

import com.jcloud.admin.service.UserSyncDataService;
import com.jcloud.admin.service.sync.StepContext;
import com.jcloud.admin.service.sync.StepExecutor;
import com.jcloud.admin.service.sync.StepResult;
import com.jcloud.common.dto.UserConflictInfo;
import com.jcloud.common.dto.UserSyncPreviewData;
import com.jcloud.common.dto.UserSyncTaskStatus;
import com.jcloud.common.dto.UserValidationError;
import com.jcloud.common.entity.VimUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

/**
 * 数据获取与验证步骤执行器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FetchAndValidateStepExecutor implements StepExecutor<FetchAndValidateStepExecutor.FetchAndValidateResult> {

    private final UserSyncDataService userSyncDataService;

    @Override
    public UserSyncTaskStatus.SyncStep getStepType() {
        return UserSyncTaskStatus.SyncStep.FETCH_AND_VALIDATE;
    }

    @Override
    public StepResult<FetchAndValidateResult> execute(StepContext context) throws Exception {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行数据获取与验证步骤，任务ID: {}", context.getTaskId());

            // 1. 获取预览数据
            String previewId = context.getRequest().getPreviewId();
            if (previewId == null) {
                return StepResult.failure("预览ID不能为空");
            }

            // 从缓存或重新查询获取预览数据
            UserSyncPreviewData previewData = getPreviewData(previewId);
            if (previewData == null) {
                return StepResult.failure("预览数据不存在或已过期，预览ID: " + previewId);
            }

            // 2. 获取待同步用户列表
            List<VimUser> allUsers = previewData.getUsersToSync();
            if (allUsers == null || allUsers.isEmpty()) {
                return StepResult.success(
                        new FetchAndValidateResult(List.of(), List.of(), List.of()),
                        "没有需要同步的用户",
                        0, 0
                );
            }

            // 3. 根据请求过滤用户
            List<VimUser> usersToSync = filterUsersByRequest(allUsers, context.getRequest());

            // 4. 数据验证
            List<UserValidationError> validationErrors = validateUsers(usersToSync);
            List<VimUser> validUsers = filterValidUsers(usersToSync, validationErrors);

            // 5. 冲突检测
            List<UserConflictInfo> conflicts = detectConflicts(validUsers);
            List<VimUser> finalUsers = filterConflictUsers(validUsers, conflicts, context.getRequest());

            // 6. 计算不匹配的用户（验证失败 + 冲突用户）
            List<VimUser> invalidUsers = usersToSync.stream()
                    .filter(user -> !finalUsers.contains(user))
                    .collect(Collectors.toList());

            // 7. 保存结果到上下文
            context.setSharedData("usersToSync", finalUsers);
            context.setSharedData("invalidUsers", invalidUsers); // 新增：不匹配的用户
            context.setSharedData("validationErrors", validationErrors);
            context.setSharedData("conflicts", conflicts);

            long duration = System.currentTimeMillis() - startTime;

            FetchAndValidateResult result = new FetchAndValidateResult(
                    finalUsers, validationErrors, conflicts
            );

            String message = String.format("数据获取与验证完成，待同步用户: %d, 验证错误: %d, 冲突: %d",
                    finalUsers.size(), validationErrors.size(), conflicts.size());

            log.info("数据获取与验证步骤完成，任务ID: {}, 耗时: {}ms, 结果: {}",
                    context.getTaskId(), duration, message);

            return StepResult.<FetchAndValidateResult>builder()
                    .success(true)
                    .data(result)
                    .message(message)
                    .totalCount(allUsers.size())
                    .successCount(finalUsers.size())
                    .failureCount(validationErrors.size())
                    .skipCount(conflicts.size())
                    .duration(duration)
                    .build();

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("数据获取与验证步骤执行失败，任务ID: {}, 耗时: {}ms", context.getTaskId(), duration, e);
            return StepResult.failure("数据获取与验证失败: " + e.getMessage(), e.toString());
        }
    }

    /**
     * 获取预览数据
     */
    private UserSyncPreviewData getPreviewData(String previewId) {
        try {
            return userSyncDataService.getPreviewData(previewId);
        } catch (Exception e) {
            log.error("获取预览数据失败: previewId={}", previewId, e);
            return null;
        }
    }

    /**
     * 根据请求过滤用户
     */
    private List<VimUser> filterUsersByRequest(List<VimUser> allUsers, com.jcloud.common.dto.UserSyncExecuteRequest request) {
        if (request.getSelectedUserPhones() == null || request.getSelectedUserPhones().isEmpty()) {
            return allUsers;
        }

        return allUsers.stream()
                .filter(user -> request.getSelectedUserPhones().contains(user.getPhone()))
                .collect(Collectors.toList());
    }

    /**
     * 验证用户数据
     */
    private List<UserValidationError> validateUsers(List<VimUser> users) {
        try {
            return userSyncDataService.validateUsers(users);
        } catch (Exception e) {
            log.error("验证用户数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 过滤有效用户（移除有致命错误的用户）
     */
    private List<VimUser> filterValidUsers(List<VimUser> users, List<UserValidationError> errors) {
        if (errors.isEmpty()) {
            return users;
        }

        // 获取有致命错误的用户标识
        List<String> invalidUserIdentifiers = errors.stream()
                .filter(error -> error.getErrorLevel() == UserValidationError.ErrorLevel.FATAL ||
                        error.getErrorLevel() == UserValidationError.ErrorLevel.ERROR)
                .map(UserValidationError::getUserIdentifier)
                .toList();

        // 过滤掉有致命错误的用户
        return users.stream()
                .filter(user -> !invalidUserIdentifiers.contains(user.getPhone()) &&
                        !invalidUserIdentifiers.contains(user.getUsername()))
                .collect(Collectors.toList());
    }

    /**
     * 检测冲突
     */
    private List<UserConflictInfo> detectConflicts(List<VimUser> users) {
        try {
            return userSyncDataService.detectConflicts(users);
        } catch (Exception e) {
            log.error("检测冲突失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 过滤冲突用户（根据冲突策略处理）
     */
    private List<VimUser> filterConflictUsers(List<VimUser> users, List<UserConflictInfo> conflicts,
                                              com.jcloud.common.dto.UserSyncExecuteRequest request) {
        if (conflicts.isEmpty()) {
            return users;
        }

        // 根据冲突策略处理
        return switch (request.getConflictStrategy()) {
            case SKIP -> {
                // 跳过有冲突的用户
                List<String> conflictUserPhones = conflicts.stream()
                        .map(conflict -> conflict.getSyncUser().getPhone())
                        .toList();
                yield users.stream()
                        .filter(user -> !conflictUserPhones.contains(user.getPhone()))
                        .collect(Collectors.toList());
            }
            case OVERWRITE, MERGE, CREATE_NEW ->
                // 这些策略允许处理冲突用户，保留所有用户
                    users;
            default -> users;
        };
    }

    /**
     * 数据获取与验证结果
     *
     * @param usersToSync Getters
     */
    public record FetchAndValidateResult(List<VimUser> usersToSync, List<UserValidationError> validationErrors,
                                         List<UserConflictInfo> conflicts) {

    }
}

package com.jcloud.admin.service.sync.steps;

import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.service.sync.StepContext;
import com.jcloud.admin.service.sync.StepExecutor;
import com.jcloud.admin.service.sync.StepResult;
import com.jcloud.common.dto.UserSyncTaskStatus;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysUserMapper;
import com.jcloud.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量插入用户步骤执行器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InsertUsersStepExecutor implements StepExecutor<InsertUsersStepExecutor.InsertUsersResult> {

    private final SysUserService sysUserService;
    private final SysUserMapper sysUserMapper;

    @Override
    public UserSyncTaskStatus.SyncStep getStepType() {
        return UserSyncTaskStatus.SyncStep.INSERT_USERS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StepResult<InsertUsersResult> execute(StepContext context) throws Exception {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行批量插入用户步骤，任务ID: {}", context.getTaskId());

            // 1. 从上下文获取预处理的用户数据
            @SuppressWarnings("unchecked")
            List<SysUser> newUsers = context.getSharedData("newUsers", List.class);
            @SuppressWarnings("unchecked")
            List<VimUser> newVimUsers = context.getSharedData("newVimUsers", List.class);

            if (newUsers == null || newUsers.isEmpty()) {
                return StepResult.success(
                        new InsertUsersResult(new ArrayList<>(), new HashMap<>()),
                        "没有需要插入的用户",
                        0, 0
                );
            }

            if (newUsers.size() != newVimUsers.size()) {
                throw new IllegalArgumentException("新用户列表和VIM用户列表大小不匹配");
            }

            // 2. 执行批量插入
            List<SysUser> savedUsers = executeBatchUserInsert(newUsers);

            // 3. 创建用户ID映射（VimUser -> SysUser ID）
            Map<String, Long> userIdMapping = createUserIdMapping(newVimUsers, savedUsers);

            // 4. 保存结果到上下文
            context.setSharedData("savedUsers", savedUsers);
            context.setSharedData("userIdMapping", userIdMapping);

            long duration = System.currentTimeMillis() - startTime;

            InsertUsersResult result = new InsertUsersResult(savedUsers, userIdMapping);

            String message = String.format("批量插入用户完成，成功插入: %d 个用户", savedUsers.size());

            log.info("批量插入用户步骤完成，任务ID: {}, 耗时: {}ms, 结果: {}",
                    context.getTaskId(), duration, message);

            return StepResult.<InsertUsersResult>builder()
                    .success(true)
                    .data(result)
                    .message(message)
                    .totalCount(newUsers.size())
                    .successCount(savedUsers.size())
                    .duration(duration)
                    .build();

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("批量插入用户步骤执行失败，任务ID: {}, 耗时: {}ms", context.getTaskId(), duration, e);
            return StepResult.failure("批量插入用户失败: " + e.getMessage(), e.toString());
        }
    }

    /**
     * 执行批量用户插入
     */
    private List<SysUser> executeBatchUserInsert(List<SysUser> users) {
        if (users.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始批量插入 {} 个用户（包含密码和指定ID）", users.size());

        // 验证所有用户都有预设的ID（优化：确保转换逻辑正确）
        for (SysUser user : users) {
            if (user.getId() == null) {
                throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED,
                    "批量插入前用户ID为空，请检查VimUser转换逻辑: " + user.getUsername());
            }
        }

        // 根据用户数量动态调整批次大小
        int batchSize = calculateOptimalBatchSize(users.size());
        List<SysUser> allInsertedUsers = new ArrayList<>();

        // 分批处理
        for (int i = 0; i < users.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, users.size());
            List<SysUser> batch = users.subList(i, endIndex);

            log.debug("处理批次 {}/{}, 用户数: {}", (i / batchSize) + 1,
                (users.size() + batchSize - 1) / batchSize, batch.size());

            try {
                // 执行批量插入
                int insertCount = sysUserMapper.insertBatch(batch);

                if (insertCount != batch.size()) {
                    throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED,
                        String.format("批次插入失败，期望插入 %d 个，实际插入 %d 个", batch.size(), insertCount));
                }

                // 验证ID保持（优化：ID已预先设置，验证是否保持不变）
                for (SysUser user : batch) {
                    if (user.getId() == null) {
                        throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED,
                            "批量插入后用户ID丢失，这不应该发生: " + user.getUsername() +
                            " (请检查VimUser转换逻辑是否正确设置了ID)");
                    }
                }

                allInsertedUsers.addAll(batch);
                log.debug("批次插入完成，成功插入 {} 个用户", insertCount);

            } catch (Exception e) {
                log.error("批次插入失败，批次范围: {}-{}", i, endIndex - 1, e);
                throw new BusinessException(ResultCode.BATCH_OPERATION_FAILED.getCode(),
                    "批次插入用户失败: " + e.getMessage(), e);
            }
        }

        log.info("批量插入用户完成，总计成功插入 {} 个用户", allInsertedUsers.size());
        return allInsertedUsers;
    }

    /**
     * 计算最优批次大小
     *
     * 优化说明：
     * 1. 减小批次大小，提高处理速度，避免前端超时
     * 2. 根据数据量动态调整，平衡性能和稳定性
     * 3. 超大数据量时使用更小的批次，确保事务不会过长
     */
    private int calculateOptimalBatchSize(int totalUsers) {
        // 根据用户数量动态调整批次大小（优化：减小批次大小）
        if (totalUsers <= 20) {
            return totalUsers; // 小量数据一次处理
        } else if (totalUsers <= 100) {
            return 20; // 中等数据量，减小批次
        } else if (totalUsers <= 500) {
            return 30; // 大数据量，适中批次
        } else if (totalUsers <= 2000) {
            return 50; // 超大数据量，较小批次
        } else {
            return 100; // 海量数据，最大批次限制
        }
    }

    /**
     * 创建用户ID映射
     */
    private Map<String, Long> createUserIdMapping(List<VimUser> vimUsers, List<SysUser> savedUsers) {
        Map<String, Long> mapping = new HashMap<>();

        if (vimUsers.size() != savedUsers.size()) {
            log.warn("VIM用户列表和保存用户列表大小不匹配: {} vs {}", vimUsers.size(), savedUsers.size());
            return mapping;
        }

        for (int i = 0; i < vimUsers.size(); i++) {
            VimUser vimUser = vimUsers.get(i);
            SysUser savedUser = savedUsers.get(i);

            if (vimUser.getPhone() != null && savedUser.getId() != null) {
                mapping.put(vimUser.getPhone(), savedUser.getId());
            }
        }

        log.debug("创建用户ID映射完成，映射数量: {}", mapping.size());
        return mapping;
    }

    /**
     * 批量插入用户结果
     *
     * @param savedUsers Getters
     */
    public record InsertUsersResult(List<SysUser> savedUsers, Map<String, Long> userIdMapping) {

    }
}

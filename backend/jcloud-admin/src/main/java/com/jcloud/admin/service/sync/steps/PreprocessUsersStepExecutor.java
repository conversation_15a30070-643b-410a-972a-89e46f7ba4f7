package com.jcloud.admin.service.sync.steps;

import com.jcloud.admin.service.sync.StepExecutor;
import com.jcloud.admin.service.sync.StepContext;
import com.jcloud.admin.service.sync.StepResult;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.dto.UserSyncTaskStatus;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户数据预处理步骤执行器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PreprocessUsersStepExecutor implements StepExecutor<PreprocessUsersStepExecutor.PreprocessResult> {

    private final SysUserService sysUserService;
    private final BCryptPasswordEncoder passwordEncoder;
    
    @Override
    public UserSyncTaskStatus.SyncStep getStepType() {
        return UserSyncTaskStatus.SyncStep.PREPROCESS_USERS;
    }
    
    @Override
    public StepResult<PreprocessResult> execute(StepContext context) throws Exception {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行用户数据预处理步骤，任务ID: {}", context.getTaskId());
            
            // 1. 从上下文获取待处理用户
            @SuppressWarnings("unchecked")
            List<VimUser> usersToSync = context.getSharedData("usersToSync", List.class);
            if (usersToSync == null || usersToSync.isEmpty()) {
                return StepResult.success(
                    new PreprocessResult(new ArrayList<>(), new ArrayList<>(), new ArrayList<>()),
                    "没有需要预处理的用户",
                    0, 0
                );
            }
            
            // 2. 转换VimUser到SysUser
            Long tenantId = context.getTenantId();
            List<SysUser> sysUsers = convertVimUsersToSysUsers(usersToSync, tenantId);
            
            // 3. 查询已存在的用户
            List<String> phones = usersToSync.stream()
                .map(VimUser::getPhone)
                .collect(Collectors.toList());
            Map<String, SysUser> existingUsers = sysUserService.batchQueryExistingUsersByPhone(phones, tenantId);
            
            // 4. 分类用户：新用户、已存在用户
            List<SysUser> newUsers = new ArrayList<>();
            List<VimUser> newVimUsers = new ArrayList<>();
            List<VimUser> agentUsers = new ArrayList<>();
            
            for (int i = 0; i < usersToSync.size(); i++) {
                VimUser vimUser = usersToSync.get(i);
                SysUser sysUser = sysUsers.get(i);
                
                // 检查是否已存在
                if (!existingUsers.containsKey(vimUser.getPhone())) {
                    newUsers.add(sysUser);
                    newVimUsers.add(vimUser);
                    
                    // 检查是否是代理用户
                    if (vimUser.getIdentity() != null && vimUser.getIdentity() == 4) {
                        agentUsers.add(vimUser);
                    }
                }
            }
            
            // 5. 为新用户预设置密码
            prepareUsersWithPasswords(newUsers, newVimUsers);
            
            // 6. 保存结果到上下文
            context.setSharedData("newUsers", newUsers);
            context.setSharedData("newVimUsers", newVimUsers);
            context.setSharedData("agentUsers", agentUsers);
            context.setSharedData("existingUsers", existingUsers);
            
            long duration = System.currentTimeMillis() - startTime;
            
            PreprocessResult result = new PreprocessResult(newUsers, newVimUsers, agentUsers);
            
            String message = String.format("用户数据预处理完成，新用户: %d, 代理用户: %d, 已存在用户: %d", 
                newUsers.size(), agentUsers.size(), existingUsers.size());
            
            log.info("用户数据预处理步骤完成，任务ID: {}, 耗时: {}ms, 结果: {}", 
                context.getTaskId(), duration, message);
            
            return StepResult.<PreprocessResult>builder()
                .success(true)
                .data(result)
                .message(message)
                .totalCount(usersToSync.size())
                .successCount(newUsers.size())
                .skipCount(existingUsers.size())
                .duration(duration)
                .build();
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("用户数据预处理步骤执行失败，任务ID: {}, 耗时: {}ms", context.getTaskId(), duration, e);
            return StepResult.failure("用户数据预处理失败: " + e.getMessage(), e.toString());
        }
    }
    
    /**
     * 转换VimUser到SysUser
     */
    private List<SysUser> convertVimUsersToSysUsers(List<VimUser> vimUsers, Long tenantId) {
        return vimUsers.stream()
            .map(vimUser -> convertVimUserToSysUser(vimUser, tenantId))
            .collect(Collectors.toList());
    }
    
    /**
     * 转换单个VimUser到SysUser
     *
     * 优化说明：
     * 1. 直接使用VimUser的ID作为SysUser的ID，避免自动生成ID的问题
     * 2. 与SysUserService中的转换逻辑保持一致
     * 3. 简化同步逻辑，确保数据一致性
     */
    private SysUser convertVimUserToSysUser(VimUser vimUser, Long tenantId) {
        SysUser sysUser = new SysUser();

        // 关键优化：直接使用VimUser的ID作为SysUser的ID
        if (vimUser.getId() != null) {
            sysUser.setId(vimUser.getId().longValue());
        }

        // 基本信息
        sysUser.setTenantId(tenantId);
        sysUser.setUsername(vimUser.getPhone()); // 使用手机号作为用户名
        sysUser.setNickname(vimUser.getNickname());
        sysUser.setRealName(vimUser.getNickname()); // 使用昵称作为真实姓名
        sysUser.setPhone(vimUser.getPhone());
        sysUser.setAvatar(vimUser.getUserimage());
        sysUser.setLastLoginIp(vimUser.getLastLoginIp());

        // 状态映射：vim_user.state (1=正常,2=禁用) -> sys_user.status (1=启用,0=禁用)
        sysUser.setStatus(vimUser.getState() != null && vimUser.getState() == 1 ? 1 : 0);

        // 默认值设置
        sysUser.setIsAdmin(0); // 非管理员
        sysUser.setGender(0); // 未知性别

        // 根据身份类型设置备注
        String identityDesc = getIdentityDescription(vimUser.getIdentity());
        sysUser.setRemark("从vim_user同步，身份: " + identityDesc + " (vim_user_id=" + vimUser.getId() + ")");

        // 设置用户身份
        sysUser.setIdentity(vimUser.getIdentity());

        // 时间转换：int时间戳 -> Long时间戳
        if (vimUser.getLastLoginTime() != null && vimUser.getLastLoginTime() > 0) {
            sysUser.setLastLoginTime(vimUser.getLastLoginTime().longValue());
        }

        if (vimUser.getCreateTime() != null && vimUser.getCreateTime() > 0) {
            Long createTime = vimUser.getCreateTime().longValue();
            sysUser.setCreateTime(createTime);
            sysUser.setUpdateTime(createTime);
        }

        // 设置创建信息
        Long currentUserId = SecurityUtils.getUserId();
        sysUser.setCreateBy(currentUserId);
        sysUser.setUpdateBy(currentUserId);

        return sysUser;
    }
    
    /**
     * 获取身份描述
     */
    private String getIdentityDescription(Integer identity) {
        if (identity == null) {
            return "未知身份";
        }
        return switch (identity) {
            case 1 -> "普通用户";
            case 2 -> "线上主播";
            case 3 -> "线下主播";
            case 4 -> "代理";
            default -> "未知身份";
        };
    }
    
    /**
     * 为用户预设置密码
     */
    private void prepareUsersWithPasswords(List<SysUser> users, List<VimUser> vimUsers) {
        if (users.size() != vimUsers.size()) {
            throw new IllegalArgumentException("用户列表和VIM用户列表大小不匹配");
        }
        
        log.debug("开始预处理 {} 个用户的密码", users.size());
        
        for (int i = 0; i < users.size(); i++) {
            SysUser user = users.get(i);
            VimUser vimUser = vimUsers.get(i);
            
            // 生成密码（使用手机号后6位 + "voltskins"）
            String password = generatePasswordByPhone(vimUser.getPhone());
            String encodedPassword = passwordEncoder.encode(password);
            
            // 直接设置到用户对象中
            user.setPassword(encodedPassword);
            user.setPasswordUpdateTime(System.currentTimeMillis());
        }
        
        log.debug("用户密码预处理完成");
    }
    
    /**
     * 根据手机号生成密码
     */
    private String generatePasswordByPhone(String phone) {
        if (phone == null || phone.length() < 6) {
            return "123456voltskins"; // 默认密码
        }
        return phone.substring(phone.length() - 6) + "voltskins";
    }
    
    /**
     * 用户数据预处理结果
     */
    public static class PreprocessResult {
        private final List<SysUser> newUsers;
        private final List<VimUser> newVimUsers;
        private final List<VimUser> agentUsers;
        
        public PreprocessResult(List<SysUser> newUsers, List<VimUser> newVimUsers, List<VimUser> agentUsers) {
            this.newUsers = newUsers;
            this.newVimUsers = newVimUsers;
            this.agentUsers = agentUsers;
        }
        
        // Getters
        public List<SysUser> getNewUsers() { return newUsers; }
        public List<VimUser> getNewVimUsers() { return newVimUsers; }
        public List<VimUser> getAgentUsers() { return agentUsers; }
    }
}

-- 创建GetSubordinateProfit存储过程
-- 递归查找指定用户的所有下级用户并计算统计数据
-- 支持超级管理员模式（p_max_level = 0）

DELIMITER $$

DROP PROCEDURE IF EXISTS GetSubordinateProfit$$

CREATE DEFINER=`vimbox`@`%` PROCEDURE GetSubordinateProfit(
    IN p_root_uid INT,
    IN p_max_level INT,
    IN p_start_time VARCHAR(20),
    IN p_end_time VARCHAR(20)
)
BEGIN
    -- 声明变量
    DECLARE v_start_time INT;
    DECLARE v_end_time INT;
    DECLARE v_start_time_ms BIGINT;
    DECLARE v_end_time_ms BIGINT;
    DECLARE v_total_recharge DECIMAL(20, 2) DEFAULT 0;
    DECLARE v_total_shipped_cost DECIMAL(20, 2) DEFAULT 0;
    DECLARE v_total_pending_cost DECIMAL(20, 2) DEFAULT 0;
    DECLARE v_total_backpack_cost DECIMAL(20, 2) DEFAULT 0;
    DECLARE v_total_profit DECIMAL(20, 2) DEFAULT 0;
    DECLARE v_total_coin_key DECIMAL(20, 2) DEFAULT 0;

    -- 创建临时表存储所有下级用户ID
    DROP TEMPORARY TABLE IF EXISTS temp_subordinate_users;
    CREATE TEMPORARY TABLE temp_subordinate_users (
        id INT PRIMARY KEY,
        level INT
    );

    -- 处理p_max_level为0的特殊情况（超级管理员模式）
    IF p_max_level = 0 THEN
        -- 直接获取所有identity是2或3的用户的下级(仅一层)
        INSERT INTO temp_subordinate_users
        SELECT p_root_uid, 0;

        INSERT INTO temp_subordinate_users
        SELECT vu.id, 1
        FROM vim_user vu
        JOIN vim_user invite ON vu.invite_user = invite.id
        WHERE invite.identity IN (2, 3)
        AND vu.id != p_root_uid;
    ELSE
        -- 原来的递归逻辑
        -- 创建另一个临时表用于存储当前级别的用户
        DROP TEMPORARY TABLE IF EXISTS temp_current_level;
        CREATE TEMPORARY TABLE temp_current_level (
            id INT PRIMARY KEY
        );

        -- 插入根用户
        INSERT INTO temp_subordinate_users VALUES (p_root_uid, 0);
        INSERT INTO temp_current_level VALUES (p_root_uid);

        -- 递归查找下级用户
        SET @current_level = 0;
        WHILE @current_level < p_max_level DO
            -- 创建临时表存储下一级用户
            DROP TEMPORARY TABLE IF EXISTS temp_next_level;
            CREATE TEMPORARY TABLE temp_next_level (
                id INT PRIMARY KEY
            );

            -- 查找下一级用户
            INSERT INTO temp_next_level
            SELECT vu.id
            FROM vim_user vu
            JOIN temp_current_level tcl ON vu.invite_user = tcl.id
            WHERE vu.id NOT IN (SELECT id FROM temp_subordinate_users);

            -- 如果没有找到更多下级，提前退出循环
            IF (SELECT COUNT(*) FROM temp_next_level) = 0 THEN
                SET @current_level = p_max_level; -- 强制退出循环
            ELSE
                -- 将下一级用户添加到主表
                INSERT INTO temp_subordinate_users
                SELECT id, @current_level + 1
                FROM temp_next_level;

                -- 准备下一次迭代
                DROP TEMPORARY TABLE IF EXISTS temp_current_level;
                CREATE TEMPORARY TABLE temp_current_level AS
                SELECT id FROM temp_next_level;

                SET @current_level = @current_level + 1;
            END IF;

            DROP TEMPORARY TABLE IF EXISTS temp_next_level;
        END WHILE;

        DROP TEMPORARY TABLE IF EXISTS temp_current_level;
    END IF;

    -- 转换时间参数为时间戳
    SET v_start_time = p_start_time;
    SET v_end_time = p_end_time;
    SET v_start_time_ms = v_start_time * 1000;
    SET v_end_time_ms = v_end_time * 1000 + 999;

    -- 计算总充值金额
    SELECT COALESCE(ROUND(SUM(amount), 2), 0) INTO v_total_recharge
    FROM vim_order_recharge
    WHERE state = 2
        AND create_time BETWEEN v_start_time AND v_end_time
        AND uid IN (SELECT id FROM temp_subordinate_users WHERE level > 0);

    -- 计算总发货成本
    SELECT COALESCE(ROUND(SUM(cost), 2), 0) INTO v_total_shipped_cost
    FROM vim_order_claim
    WHERE cost > 0
        AND claim_time BETWEEN v_start_time AND v_end_time
        AND uid IN (SELECT id FROM temp_subordinate_users WHERE level > 0);

    -- 计算总待发货成本
    SELECT COALESCE(ROUND(SUM(vi.price_cost), 2), 0) INTO v_total_pending_cost
    FROM vim_order_box vob
    JOIN vim_item vi ON vob.itemid = vi.id
    WHERE vob.state = 3
        AND vob.TIMESTAMP BETWEEN v_start_time_ms AND v_end_time_ms
        AND vob.uid IN (SELECT id FROM temp_subordinate_users WHERE level > 0);

    -- 计算总背包成本
    SELECT COALESCE(ROUND(SUM(vi.price_cost), 2), 0) INTO v_total_backpack_cost
    FROM vim_order_box vob
    JOIN vim_item vi ON vob.itemid = vi.id
    WHERE vob.state = 1
        AND vob.TIMESTAMP BETWEEN v_start_time_ms AND v_end_time_ms
        AND vob.uid IN (SELECT id FROM temp_subordinate_users WHERE level > 0);

    -- 计算所有下级用户的coin和key总余额
    SELECT COALESCE(ROUND(SUM(coin + `key`), 2), 0) INTO v_total_coin_key
    FROM vim_user
    WHERE id IN (SELECT id FROM temp_subordinate_users WHERE level > 0);

    -- 计算总利润
    SET v_total_profit = ROUND(v_total_recharge - v_total_shipped_cost - v_total_pending_cost - v_total_backpack_cost - v_total_coin_key, 2);

    -- 返回结果（使用中文列名）
    SELECT
        COUNT(DISTINCT id) - 1 AS 'zongyonghu',
        v_total_recharge AS 'zongchongzhi',
        v_total_profit AS 'zonglirun'
    FROM temp_subordinate_users;

    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_subordinate_users;
    
END$$

DELIMITER ;

-- 使用示例：
-- CALL GetSubordinateProfit(1, 5, '1640995200', '1672531199');
-- 参数说明：
-- 1: 根用户ID
-- 5: 最大查询层级
-- '1640995200': 开始时间戳（字符串格式）
-- '1672531199': 结束时间戳（字符串格式）

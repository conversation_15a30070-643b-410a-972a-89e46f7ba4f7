package com.jcloud.common.dto;

import com.jcloud.common.entity.VimUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;
import java.util.ArrayList;

/**
 * 批量操作结果
 * 
 * @param <T> 操作的数据类型
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "批量操作结果")
public class BatchOperationResult<T> {
    
    /**
     * 总数量
     */
    @Schema(description = "总数量")
    private int totalCount;
    
    /**
     * 成功数量
     */
    @Schema(description = "成功数量")
    private int successCount;
    
    /**
     * 失败数量
     */
    @Schema(description = "失败数量")
    private int failureCount;
    
    /**
     * 跳过数量（如重复数据）
     */
    @Schema(description = "跳过数量")
    private int skipCount;
    
    /**
     * 成功的项目列表
     */
    @Schema(description = "成功的项目列表")
    @Builder.Default
    private List<T> successItems = new ArrayList<>();
    
    /**
     * 失败的项目和错误信息
     */
    @Schema(description = "失败的项目和错误信息")
    @Builder.Default
    private List<BatchOperationError> errors = new ArrayList<>();
    
    /**
     * 操作是否完全成功
     */
    @JsonIgnore
    @Schema(description = "操作是否完全成功")
    public boolean isCompleteSuccess() {
        return failureCount == 0 && totalCount > 0;
    }

    /**
     * 操作是否部分成功
     */
    @JsonIgnore
    @Schema(description = "操作是否部分成功")
    public boolean isPartialSuccess() {
        return successCount > 0 && failureCount > 0;
    }

    /**
     * 操作是否完全失败
     */
    @JsonIgnore
    @Schema(description = "操作是否完全失败")
    public boolean isCompleteFailure() {
        return successCount == 0 && totalCount > 0;
    }

    /**
     * 获取成功率
     */
    @JsonIgnore
    @Schema(description = "成功率")
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount;
    }
    
    /**
     * 添加成功项目
     */
    public void addSuccessItem(T item) {
        if (successItems == null) {
            successItems = new ArrayList<>();
        }
        successItems.add(item);
        successCount++;
    }
    
    /**
     * 添加失败项目
     */
    public void addFailureItem(Object item, String errorCode, String errorMessage) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(BatchOperationError.builder()
                .item(item)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build());
        failureCount++;
    }
    
    /**
     * 添加失败项目（带异常）
     */
    public void addFailureItem(Object item, String errorCode, String errorMessage, Throwable throwable) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(BatchOperationError.builder()
                .item(item)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .throwable(throwable)
                .build());
        failureCount++;
    }

    /**
     * 添加失败项目（带标识符）
     */
    public void addFailureItem(Object item, String itemIdentifier, String errorCode, String errorMessage) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(BatchOperationError.builder()
                .item(item)
                .itemIdentifier(itemIdentifier)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build());
        failureCount++;
    }

    /**
     * 添加失败项目（带标识符和异常）
     */
    public void addFailureItem(Object item, String itemIdentifier, String errorCode, String errorMessage, Throwable throwable) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(BatchOperationError.builder()
                .item(item)
                .itemIdentifier(itemIdentifier)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .throwable(throwable)
                .build());
        failureCount++;
    }
    
    /**
     * 增加跳过数量
     */
    public void addSkipCount(int count) {
        this.skipCount += count;
    }
    
    /**
     * 设置总数量并自动计算其他统计信息
     */
    public void setTotalCountAndCalculate(int totalCount) {
        this.totalCount = totalCount;
        // 重新计算失败数量，确保数据一致性
        this.failureCount = errors != null ? errors.size() : 0;
    }
}

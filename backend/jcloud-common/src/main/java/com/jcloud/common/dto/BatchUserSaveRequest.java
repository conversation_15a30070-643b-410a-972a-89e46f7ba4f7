package com.jcloud.common.dto;

import com.jcloud.common.entity.VimUser;
import com.jcloud.common.enums.BatchTransactionMode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量用户保存请求
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "批量用户保存请求")
public class BatchUserSaveRequest {
    
    /**
     * 用户列表
     */
    @Schema(description = "用户列表", required = true)
    @NotEmpty(message = "用户列表不能为空")
    @Valid
    private List<VimUser> users;
    
    /**
     * 事务模式（默认严格模式，性能最佳）
     */
    @Schema(description = "事务模式：STRICT(严格模式-推荐，性能最佳), LENIENT(宽松模式-兼容模式)",
            example = "STRICT",
            defaultValue = "STRICT")
    @NotNull(message = "事务模式不能为空")
    private BatchTransactionMode mode = BatchTransactionMode.getDefault();
    
    /**
     * 是否跳过已存在的用户
     */
    @Schema(description = "是否跳过已存在的用户", defaultValue = "true")
    private Boolean skipExisting = true;
    
    /**
     * 批次大小（用于性能优化）
     */
    @Schema(description = "批次大小，用于分批处理大量数据", defaultValue = "100", minimum = "1", maximum = "1000")
    private Integer batchSize = 100;
    
    /**
     * 是否异步处理
     */
    @Schema(description = "是否异步处理（大批量数据建议使用异步）", defaultValue = "false")
    private Boolean async = false;
}

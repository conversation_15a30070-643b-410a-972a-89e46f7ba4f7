package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 模拟登录请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "模拟登录请求")
public class ImpersonationRequest {
    
    /**
     * 目标用户ID
     */
    @NotNull(message = "目标用户ID不能为空")
    @Schema(description = "目标用户ID", requiredMode= Schema.RequiredMode.REQUIRED)
    private Long targetUserId;
    
    /**
     * 模拟原因
     */
    @NotBlank(message = "模拟原因不能为空")
    @Size(max = 500, message = "模拟原因长度不能超过500字符")
    @Schema(description = "模拟原因", requiredMode= Schema.RequiredMode.REQUIRED, maxLength = 500)
    private String reason;
}

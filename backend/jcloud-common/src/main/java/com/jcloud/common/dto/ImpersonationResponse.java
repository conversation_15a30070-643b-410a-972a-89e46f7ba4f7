package com.jcloud.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 模拟登录响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "模拟登录响应")
public class ImpersonationResponse {
    
    /**
     * 新的访问令牌
     */
    @Schema(description = "新的访问令牌")
    private String token;
    
    /**
     * 是否处于模拟状态
     */
    @Schema(description = "是否处于模拟状态")
    private Boolean impersonating;
    
    /**
     * 目标用户ID
     */
    @Schema(description = "目标用户ID")
    private Long targetUserId;
    
    /**
     * 目标用户名
     */
    @Schema(description = "目标用户名")
    private String targetUsername;
    
    /**
     * 原管理员用户ID
     */
    @Schema(description = "原管理员用户ID")
    private Long adminUserId;
    
    /**
     * 原管理员用户名
     */
    @Schema(description = "原管理员用户名")
    private String adminUsername;
    
    /**
     * 模拟开始时间（秒级时间戳）
     */
    @Schema(description = "模拟开始时间（秒级时间戳）")
    private Long startTime;
    
    /**
     * 模拟原因
     */
    @Schema(description = "模拟原因")
    private String reason;
    
    /**
     * 提示消息
     */
    @Schema(description = "提示消息")
    private String bannerMessage;
}

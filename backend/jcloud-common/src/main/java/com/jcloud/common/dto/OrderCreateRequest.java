package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单创建请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "订单创建请求")
public class OrderCreateRequest {
    
    /**
     * 结算开始时间（时间戳，秒）
     */
    @Schema(description = "结算开始时间", example = "1640995200")
    @NotNull(message = "结算开始时间不能为空")
    @Min(value = 1, message = "结算开始时间必须大于0")
    private Long startTime;
    
    /**
     * 结算结束时间（时间戳，秒）
     */
    @Schema(description = "结算结束时间", example = "1641081600")
    @NotNull(message = "结算结束时间不能为空")
    @Min(value = 1, message = "结算结束时间必须大于0")
    private Long endTime;
    
    /**
     * 劳务比例（百分比，如10表示10%）
     */
    @Schema(description = "劳务比例", example = "10.00")
    @NotNull(message = "劳务比例不能为空")
    @DecimalMin(value = "0.01", message = "劳务比例必须大于0.01%")
    @DecimalMax(value = "100.00", message = "劳务比例不能超过100%")
    @Digits(integer = 3, fraction = 2, message = "劳务比例格式不正确")
    private BigDecimal feeRate;
    
    /**
     * 收款人姓名
     */
    @Schema(description = "收款人姓名", example = "张三")
    @NotBlank(message = "收款人姓名不能为空")
    @Size(max = 50, message = "收款人姓名长度不能超过50个字符")
    private String feePerson;
    
    /**
     * 收款账户信息
     */
    @Schema(description = "收款账户信息", example = "6222021234567890123")
    @NotBlank(message = "收款账户信息不能为空")
    @Size(max = 100, message = "收款账户信息长度不能超过100个字符")
    private String feeAccount;

    /**
     * 目标用户ID（要为哪个用户创建订单）
     */
    @Schema(description = "目标用户ID", example = "1")
    @NotNull(message = "目标用户ID不能为空")
    @Min(value = 1, message = "目标用户ID必须大于0")
    private Long targetUserId;
    
    // ==================== 业务方法 ====================
    
    /**
     * 验证时间区间是否有效
     */
    public boolean isValidTimeRange() {
        return startTime != null && endTime != null && startTime < endTime;
    }
    
    /**
     * 获取时间区间长度（秒）
     */
    public long getTimeRangeSeconds() {
        if (!isValidTimeRange()) {
            return 0;
        }
        return endTime - startTime;
    }
    
    /**
     * 获取时间区间长度（天）
     */
    public long getTimeRangeDays() {
        return getTimeRangeSeconds() / (24 * 60 * 60);
    }
}

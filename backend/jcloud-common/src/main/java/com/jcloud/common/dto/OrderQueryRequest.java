package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单查询请求")
public class OrderQueryRequest extends PageQuery {
    
    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private String payid;
    
    /**
     * 父订单ID
     */
    @Schema(description = "父订单ID")
    private String parentsPayid;
    
    /**
     * 代理ID
     */
    @Schema(description = "代理ID")
    private String agent;
    
    /**
     * 主播ID
     */
    @Schema(description = "主播ID")
    private String anchor;
    
    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    private Integer orderStatus;
    
    /**
     * 结算状态
     */
    @Schema(description = "结算状态")
    private Integer settlementStatus;
    
    /**
     * 创建开始时间（时间戳，秒）
     */
    @Schema(description = "创建开始时间")
    private Long createTimeStart;
    
    /**
     * 创建结束时间（时间戳，秒）
     */
    @Schema(description = "创建结束时间")
    private Long createTimeEnd;
    
    /**
     * 更新开始时间（时间戳，秒）
     */
    @Schema(description = "更新开始时间")
    private Long updateTimeStart;
    
    /**
     * 更新结束时间（时间戳，秒）
     */
    @Schema(description = "更新结束时间")
    private Long updateTimeEnd;
    
    /**
     * 是否只查询主订单
     */
    @Schema(description = "是否只查询主订单")
    private Boolean mainOrderOnly;
    
    /**
     * 是否只查询子订单
     */
    @Schema(description = "是否只查询子订单")
    private Boolean subOrderOnly;
    
    // ==================== 业务方法 ====================
    
    /**
     * 判断是否有时间范围查询条件
     */
    public boolean hasTimeRange() {
        return (createTimeStart != null && createTimeEnd != null) ||
               (updateTimeStart != null && updateTimeEnd != null);
    }
    
    /**
     * 判断是否有状态查询条件
     */
    public boolean hasStatusCondition() {
        return orderStatus != null || settlementStatus != null;
    }
    
    /**
     * 判断是否有用户查询条件
     */
    public boolean hasUserCondition() {
        return agent != null || anchor != null;
    }
}

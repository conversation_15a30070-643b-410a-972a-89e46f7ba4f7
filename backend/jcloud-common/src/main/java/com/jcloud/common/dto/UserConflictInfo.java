package com.jcloud.common.dto;

import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 用户冲突信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户冲突信息")
public class UserConflictInfo {
    
    /**
     * 冲突类型
     */
    @Schema(description = "冲突类型")
    private ConflictType conflictType;
    
    /**
     * 现有用户信息
     */
    @Schema(description = "现有用户信息")
    private SysUser existingUser;
    
    /**
     * 待同步用户信息
     */
    @Schema(description = "待同步用户信息")
    private VimUser syncUser;
    
    /**
     * 冲突字段列表
     */
    @Schema(description = "冲突字段列表")
    private List<String> conflictFields;
    
    /**
     * 冲突描述
     */
    @Schema(description = "冲突描述")
    private String conflictDescription;
    
    /**
     * 建议的处理方式
     */
    @Schema(description = "建议的处理方式")
    private String suggestedAction;
    
    /**
     * 是否可以自动解决
     */
    @Schema(description = "是否可以自动解决")
    private Boolean autoResolvable;
    
    /**
     * 冲突类型枚举
     */
    public enum ConflictType {
        /**
         * 手机号冲突
         */
        PHONE_CONFLICT("手机号冲突"),
        
        /**
         * 用户名冲突
         */
        USERNAME_CONFLICT("用户名冲突"),
        
        /**
         * 邮箱冲突
         */
        EMAIL_CONFLICT("邮箱冲突"),
        
        /**
         * 多字段冲突
         */
        MULTIPLE_CONFLICT("多字段冲突"),
        
        /**
         * 数据不一致
         */
        DATA_INCONSISTENT("数据不一致");
        
        private final String description;
        
        ConflictType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}

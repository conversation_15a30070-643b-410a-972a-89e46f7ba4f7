package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户查询请求")
public class UserQueryRequest extends PageQuery {
    
    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    /**
     * 昵称
     */
    @Schema(description = "昵称", example = "管理员")
    private String nickname;
    
    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名", example = "张三")
    private String realName;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13800138000")
    private String phone;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    /**
     * 部门ID
     */
    @Schema(description = "部门ID", example = "1")
    private Long deptId;
    
    /**
     * 是否管理员（0-否，1-是）
     */
    @Schema(description = "是否管理员", example = "1")
    private Integer isAdmin;

    /**
     * 角色ID（按角色筛选用户）
     */
    @Schema(description = "角色ID", example = "1")
    private Long roleId;

    /**
     * 角色编码（按角色编码筛选用户）
     */
    @Schema(description = "角色编码", example = "ADMIN")
    private String roleCode;
    /**
     * 用户身份列表（支持多个身份查询）
     */
    @Schema(description = "用户身份列表", example = "[2,3,4]")
    private List<Integer> identities;

    /**
     * 用户身份字符串（前端传递的逗号分隔字符串）
     */
    @Schema(description = "用户身份字符串", example = "2,3,4")
    private String identitiesStr;

    /**
     * 获取身份列表，优先使用identities，如果为空则解析identitiesStr
     */
    public List<Integer> getIdentitiesList() {
        if (identities != null && !identities.isEmpty()) {
            return identities;
        }

        if (identitiesStr != null && !identitiesStr.trim().isEmpty()) {
            try {
                return Arrays.stream(identitiesStr.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            } catch (NumberFormatException e) {
                // 如果解析失败，返回空列表
                return Collections.emptyList();
            }
        }

        return Collections.emptyList();
    }
}

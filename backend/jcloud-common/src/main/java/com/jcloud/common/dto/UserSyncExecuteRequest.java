package com.jcloud.common.dto;

import com.jcloud.common.enums.BatchTransactionMode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 用户同步执行请求
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户同步执行请求")
public class UserSyncExecuteRequest {
    
    /**
     * 预览会话ID
     */
    @Schema(description = "预览会话ID", required = true)
    @NotBlank(message = "预览会话ID不能为空")
    private String previewId;
    
    /**
     * 要同步的用户手机号列表（如果为空则同步所有预览中的用户）
     */
    @Schema(description = "要同步的用户手机号列表")
    private List<String> selectedUserPhones;
    
    /**
     * 事务模式（默认严格模式，性能最佳）
     */
    @Schema(description = "事务模式：STRICT(推荐，性能最佳), LENIENT(兼容模式)",
            example = "STRICT",
            defaultValue = "STRICT")
    @NotNull(message = "事务模式不能为空")
    @Builder.Default
    private BatchTransactionMode transactionMode = BatchTransactionMode.getDefault();
    
    /**
     * 批次大小
     */
    @Schema(description = "批次大小", defaultValue = "100", minimum = "1", maximum = "1000")
    @Builder.Default
    private Integer batchSize = 100;
    
    /**
     * 是否跳过验证错误的用户
     */
    @Schema(description = "是否跳过验证错误的用户", defaultValue = "true")
    @Builder.Default
    private Boolean skipInvalidUsers = true;
    
    /**
     * 是否强制覆盖冲突用户
     */
    @Schema(description = "是否强制覆盖冲突用户", defaultValue = "false")
    @Builder.Default
    private Boolean forceOverwrite = false;
    
    /**
     * 冲突处理策略
     */
    @Schema(description = "冲突处理策略", defaultValue = "SKIP")
    @Builder.Default
    private ConflictResolutionStrategy conflictStrategy = ConflictResolutionStrategy.SKIP;
    
    /**
     * 是否异步执行
     */
    @Schema(description = "是否异步执行", defaultValue = "true")
    @Builder.Default
    private Boolean async = true;
    
    /**
     * 执行备注
     */
    @Schema(description = "执行备注")
    private String remark;
    
    /**
     * 冲突解决策略枚举
     */
    public enum ConflictResolutionStrategy {
        /**
         * 跳过冲突用户
         */
        SKIP("跳过冲突用户"),
        
        /**
         * 覆盖现有用户
         */
        OVERWRITE("覆盖现有用户"),
        
        /**
         * 合并用户信息
         */
        MERGE("合并用户信息"),
        
        /**
         * 创建新用户（添加后缀）
         */
        CREATE_NEW("创建新用户");
        
        private final String description;
        
        ConflictResolutionStrategy(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}

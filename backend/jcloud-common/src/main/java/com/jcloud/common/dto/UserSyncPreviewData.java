package com.jcloud.common.dto;

import com.jcloud.common.entity.VimUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 用户同步预览数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户同步预览数据")
public class UserSyncPreviewData {
    
    /**
     * 总用户数
     */
    @Schema(description = "总用户数")
    private int totalCount;
    
    /**
     * 新用户数（需要同步的）
     */
    @Schema(description = "新用户数")
    private int newUserCount;
    
    /**
     * 已存在用户数（跳过的）
     */
    @Schema(description = "已存在用户数")
    private int existingUserCount;
    
    /**
     * 无效用户数（验证失败的）
     */
    @Schema(description = "无效用户数")
    private int invalidUserCount;
    
    /**
     * 需要同步的用户列表
     */
    @Schema(description = "需要同步的用户列表")
    private List<VimUser> usersToSync;
    
    /**
     * 已存在的用户列表（手机号 -> 用户信息）
     */
    @Schema(description = "已存在的用户列表")
    private Map<String, UserConflictInfo> existingUsers;
    
    /**
     * 无效的用户列表
     */
    @Schema(description = "无效的用户列表")
    private List<UserValidationError> invalidUsers;
    
    /**
     * 预览生成时间
     */
    @Schema(description = "预览生成时间")
    private Long previewTime;
    
    /**
     * 预览会话ID（用于后续操作）
     */
    @Schema(description = "预览会话ID")
    private String previewId;
    
    /**
     * 数据来源信息
     */
    @Schema(description = "数据来源信息")
    private DataSourceInfo sourceInfo;
    
    /**
     * 数据来源信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "数据来源信息")
    public static class DataSourceInfo {
        
        /**
         * 来源表名
         */
        @Schema(description = "来源表名")
        private String sourceTable;
        
        /**
         * 查询时间
         */
        @Schema(description = "查询时间")
        private Long queryTime;
        
        /**
         * 查询条件
         */
        @Schema(description = "查询条件")
        private String queryCondition;
        
        /**
         * 数据版本（用于检测数据变化）
         */
        @Schema(description = "数据版本")
        private String dataVersion;
    }
}

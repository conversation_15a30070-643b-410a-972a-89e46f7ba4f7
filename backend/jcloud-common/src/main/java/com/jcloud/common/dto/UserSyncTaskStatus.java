package com.jcloud.common.dto;

import com.jcloud.common.dto.BatchOperationResult;
import com.jcloud.common.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 用户同步任务状态
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户同步任务状态")
public class UserSyncTaskStatus {
    
    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    private String taskId;
    
    /**
     * 任务状态
     */
    @Schema(description = "任务状态")
    private TaskStatus status;
    
    /**
     * 当前步骤
     */
    @Schema(description = "当前步骤")
    private SyncStep currentStep;
    
    /**
     * 总步骤数
     */
    @Schema(description = "总步骤数")
    private int totalSteps;
    
    /**
     * 当前步骤索引（从0开始）
     */
    @Schema(description = "当前步骤索引")
    private int currentStepIndex;
    
    /**
     * 进度百分比（0-100）
     */
    @Schema(description = "进度百分比")
    private int progressPercentage;
    
    /**
     * 任务开始时间
     */
    @Schema(description = "任务开始时间")
    private Long startTime;
    
    /**
     * 任务结束时间
     */
    @Schema(description = "任务结束时间")
    private Long endTime;
    
    /**
     * 执行耗时（毫秒）
     */
    @Schema(description = "执行耗时")
    private Long duration;
    
    /**
     * 当前步骤消息
     */
    @Schema(description = "当前步骤消息")
    private String currentMessage;
    
    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;
    
    /**
     * 错误详情
     */
    @Schema(description = "错误详情")
    private String errorDetail;
    
    /**
     * 步骤历史记录
     */
    @Schema(description = "步骤历史记录")
    private List<StepRecord> stepHistory;
    
    /**
     * 同步结果（任务完成后）
     */
    @Schema(description = "同步结果")
    private BatchOperationResult<SysUser> syncResult;

    /**
     * 步骤详细信息
     */
    @Schema(description = "步骤详细信息")
    private StepDetails stepDetails;
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        /**
         * 等待中
         */
        PENDING("等待中"),
        
        /**
         * 运行中
         */
        RUNNING("运行中"),
        
        /**
         * 已完成
         */
        COMPLETED("已完成"),
        
        /**
         * 失败
         */
        FAILED("失败"),
        
        /**
         * 已取消
         */
        CANCELLED("已取消"),
        
        /**
         * 超时
         */
        TIMEOUT("超时");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 同步步骤枚举（优化版本 - 6个细粒度步骤）
     */
    public enum SyncStep {
        /**
         * 数据获取与验证
         */
        FETCH_AND_VALIDATE("数据获取与验证"),

        /**
         * 用户数据预处理
         */
        PREPROCESS_USERS("用户数据预处理"),

        /**
         * 批量插入用户
         */
        INSERT_USERS("批量插入用户"),

        /**
         * 批量分配角色
         */
        ASSIGN_ROLES("批量分配角色"),

        /**
         * 处理代理部门
         */
        PROCESS_AGENT_DEPTS("处理代理部门"),

        /**
         * 完成清理
         */
        COMPLETE_CLEANUP("完成清理");

        private final String description;

        SyncStep(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 获取步骤权重（用于进度计算）
         */
        public int getWeight() {
            return switch (this) {
                case FETCH_AND_VALIDATE -> 20;    // 数据获取与验证 20%
                case PREPROCESS_USERS -> 10;      // 用户数据预处理 10%
                case INSERT_USERS -> 30;          // 批量插入用户 30%
                case ASSIGN_ROLES -> 25;          // 批量分配角色 25%
                case PROCESS_AGENT_DEPTS -> 10;   // 处理代理部门 10%
                case COMPLETE_CLEANUP -> 5;       // 完成清理 5%
            };
        }

        /**
         * 获取步骤索引
         */
        public int getIndex() {
            return this.ordinal();
        }

        /**
         * 获取总步骤数
         */
        public static int getTotalSteps() {
            return values().length;
        }
    }
    
    /**
     * 步骤记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "步骤记录")
    public static class StepRecord {
        
        /**
         * 步骤
         */
        @Schema(description = "步骤")
        private SyncStep step;
        
        /**
         * 步骤状态
         */
        @Schema(description = "步骤状态")
        private TaskStatus status;
        
        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        private Long startTime;
        
        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        private Long endTime;
        
        /**
         * 耗时
         */
        @Schema(description = "耗时")
        private Long duration;
        
        /**
         * 消息
         */
        @Schema(description = "消息")
        private String message;
        
        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String error;
    }

    /**
     * 步骤详细信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "步骤详细信息")
    public static class StepDetails {

        /**
         * 总用户数
         */
        @Schema(description = "总用户数")
        private int totalUsers;

        /**
         * 已处理用户数
         */
        @Schema(description = "已处理用户数")
        private int processedUsers;

        /**
         * 成功处理用户数
         */
        @Schema(description = "成功处理用户数")
        private int successUsers;

        /**
         * 失败用户数
         */
        @Schema(description = "失败用户数")
        private int failedUsers;

        /**
         * 跳过用户数
         */
        @Schema(description = "跳过用户数")
        private int skippedUsers;

        /**
         * 当前批次
         */
        @Schema(description = "当前批次")
        private int currentBatch;

        /**
         * 总批次数
         */
        @Schema(description = "总批次数")
        private int totalBatches;

        /**
         * 预计剩余时间（毫秒）
         */
        @Schema(description = "预计剩余时间")
        private Long estimatedRemainingTime;
    }

    /**
     * 计算基于步骤权重的进度百分比
     */
    public int calculateProgressPercentage() {
        if (currentStep == null) {
            return 0;
        }

        int completedWeight = 0;
        for (SyncStep step : SyncStep.values()) {
            if (step.getIndex() < currentStep.getIndex()) {
                completedWeight += step.getWeight();
            } else if (step == currentStep) {
                // 当前步骤的进度（可以根据stepDetails中的信息计算）
                int stepProgress = calculateCurrentStepProgress();
                completedWeight += (step.getWeight() * stepProgress) / 100;
                break;
            }
        }

        return Math.min(100, completedWeight);
    }

    /**
     * 计算当前步骤的进度百分比
     */
    private int calculateCurrentStepProgress() {
        if (stepDetails == null || stepDetails.getTotalUsers() == 0) {
            return 0;
        }

        return (stepDetails.getProcessedUsers() * 100) / stepDetails.getTotalUsers();
    }
}

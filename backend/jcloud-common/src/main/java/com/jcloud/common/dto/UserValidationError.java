package com.jcloud.common.dto;

import com.jcloud.common.entity.VimUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 用户验证错误信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户验证错误信息")
public class UserValidationError {
    
    /**
     * 用户信息
     */
    @Schema(description = "用户信息")
    private VimUser user;
    
    /**
     * 用户标识（通常是手机号或用户名）
     */
    @Schema(description = "用户标识")
    private String userIdentifier;
    
    /**
     * 错误类型
     */
    @Schema(description = "错误类型")
    private ErrorType errorType;
    
    /**
     * 错误字段列表
     */
    @Schema(description = "错误字段列表")
    private List<String> errorFields;
    
    /**
     * 错误描述
     */
    @Schema(description = "错误描述")
    private String errorMessage;
    
    /**
     * 详细错误信息
     */
    @Schema(description = "详细错误信息")
    private String detailMessage;
    
    /**
     * 修复建议
     */
    @Schema(description = "修复建议")
    private String fixSuggestion;
    
    /**
     * 错误严重级别
     */
    @Schema(description = "错误严重级别")
    private ErrorLevel errorLevel;
    
    /**
     * 是否可以忽略
     */
    @Schema(description = "是否可以忽略")
    private Boolean ignorable;
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        /**
         * 必填字段缺失
         */
        REQUIRED_FIELD_MISSING("必填字段缺失"),
        
        /**
         * 字段格式错误
         */
        INVALID_FORMAT("字段格式错误"),
        
        /**
         * 字段长度超限
         */
        LENGTH_EXCEEDED("字段长度超限"),
        
        /**
         * 无效的枚举值
         */
        INVALID_ENUM_VALUE("无效的枚举值"),
        
        /**
         * 业务规则违反
         */
        BUSINESS_RULE_VIOLATION("业务规则违反"),
        
        /**
         * 数据完整性错误
         */
        DATA_INTEGRITY_ERROR("数据完整性错误");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 错误严重级别枚举
     */
    public enum ErrorLevel {
        /**
         * 致命错误（必须修复）
         */
        FATAL("致命错误"),
        
        /**
         * 错误（建议修复）
         */
        ERROR("错误"),
        
        /**
         * 警告（可以忽略）
         */
        WARNING("警告"),
        
        /**
         * 信息（仅提示）
         */
        INFO("信息");
        
        private final String description;
        
        ErrorLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}

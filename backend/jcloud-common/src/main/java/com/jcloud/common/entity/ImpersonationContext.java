package com.jcloud.common.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 模拟登录上下文实体
 * 用于存储模拟登录的相关信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "模拟登录上下文信息")
public class ImpersonationContext {
    
    /**
     * 模拟会话Token
     */
    @Schema(description = "模拟会话Token")
    private String impersonationToken;
    
    /**
     * 原管理员用户ID
     */
    @Schema(description = "原管理员用户ID")
    private Long adminUserId;
    
    /**
     * 原管理员用户名
     */
    @Schema(description = "原管理员用户名")
    private String adminUsername;
    
    /**
     * 目标用户ID
     */
    @Schema(description = "目标用户ID")
    private Long targetUserId;
    
    /**
     * 目标用户名
     */
    @Schema(description = "目标用户名")
    private String targetUsername;
    
    /**
     * 模拟原因
     */
    @Schema(description = "模拟原因")
    private String reason;
    
    /**
     * 开始时间（秒级时间戳）
     */
    @Schema(description = "开始时间（秒级时间戳）")
    private Long startTime;
    
    /**
     * 客户端IP地址
     */
    @Schema(description = "客户端IP地址")
    private String clientIp;
    
    /**
     * 用户代理
     */
    @Schema(description = "用户代理")
    private String userAgent;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 关联ID（用于审计日志关联）
     */
    @Schema(description = "关联ID")
    private String correlationId;
}

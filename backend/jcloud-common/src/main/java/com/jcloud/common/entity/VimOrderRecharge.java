package com.jcloud.common.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Vim充值订单实体类（从库查询）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Table(value = "vim_order_recharge", dataSource = "slave")
@Schema(description = "Vim充值订单")
public class VimOrderRecharge {
    
    /**
     * 订单ID
     */
    @Id(keyType = KeyType.None)
    @Schema(description = "订单ID")
    private String id;
    
    /**
     * 支付ID
     */
    @Schema(description = "支付ID")
    private String payid;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer uid;
    
    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    private BigDecimal amount;
    
    /**
     * 获得金币
     */
    @Schema(description = "获得金币")
    private BigDecimal coin;
    
    /**
     * 订单状态（1-待支付，2-支付成功）
     */
    @Schema(description = "订单状态")
    private Integer state;
    
    /**
     * 创建时间（时间戳）
     */
    @Schema(description = "创建时间")
    private Integer createTime;
    
    /**
     * 更新时间（时间戳）
     */
    @Schema(description = "更新时间")
    private Integer updateTime;
    
    // ==================== 业务方法 ====================
    
    /**
     * 判断是否支付成功
     */
    public boolean isPaid() {
        return state != null && state == 2;
    }
    
    /**
     * 判断是否待支付
     */
    public boolean isPending() {
        return state != null && state == 1;
    }
    
    /**
     * 获取充值金额（非空安全）
     */
    public BigDecimal getSafeAmount() {
        return amount != null ? amount : BigDecimal.ZERO;
    }
    
    /**
     * 获取金币数量（非空安全）
     */
    public BigDecimal getSafeCoin() {
        return coin != null ? coin : BigDecimal.ZERO;
    }
}

package com.jcloud.common.enums;

import lombok.Getter;

/**
 * 订单状态枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum OrderStatus {
    
    /**
     * 已创建
     */
    CREATED(0, "已创建"),
    
    /**
     * 已计算
     */
    CALCULATED(1, "已计算"),
    
    /**
     * 待结算
     */
    PENDING_SETTLEMENT(2, "待结算"),
    
    /**
     * 已结算
     */
    SETTLED(3, "已结算"),
    
    /**
     * 已取消
     */
    CANCELLED(4, "已取消");
    
    private final Integer code;
    private final String description;
    
    OrderStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static OrderStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的订单状态代码: " + code);
    }
    
    /**
     * 判断是否可以进行结算
     */
    public boolean canSettle() {
        return this == CALCULATED || this == PENDING_SETTLEMENT;
    }
    
    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return this == SETTLED || this == CANCELLED;
    }
}

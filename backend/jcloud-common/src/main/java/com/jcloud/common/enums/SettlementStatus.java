package com.jcloud.common.enums;

import lombok.Getter;

/**
 * 结算状态枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum SettlementStatus {
    
    /**
     * 无需结算
     */
    NONE(0, "无需结算"),
    
    /**
     * 待处理
     */
    PENDING(1, "待处理"),
    
    /**
     * 结算成功
     */
    SUCCESS(2, "结算成功"),
    
    /**
     * 结算失败
     */
    FAILED(3, "结算失败");
    
    private final Integer code;
    private final String description;
    
    SettlementStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static SettlementStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SettlementStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的结算状态代码: " + code);
    }
    
    /**
     * 判断是否可以重新结算
     */
    public boolean canRetry() {
        return this == PENDING || this == FAILED;
    }
    
    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return this == SUCCESS;
    }
}

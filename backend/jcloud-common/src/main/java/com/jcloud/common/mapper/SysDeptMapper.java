package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysDept;
import com.jcloud.common.entity.SysUser;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 部门Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysDeptMapper extends BaseMapper<SysDept> {
    
    /**
     * 根据部门编码查询部门（包含租户过滤）
     * 
     * @param deptCode 部门编码
     * @param tenantId 租户ID
     * @return 部门信息
     */
    @Select("SELECT * FROM sys_dept WHERE dept_code = #{deptCode} AND tenant_id = #{tenantId} AND deleted = 0")
    SysDept selectByDeptCode(@Param("deptCode") String deptCode, @Param("tenantId") Long tenantId);
    
    /**
     * 根据父部门ID查询子部门列表
     * 
     * @param parentId 父部门ID
     * @param tenantId 租户ID
     * @return 子部门列表
     */
    @Select("SELECT * FROM sys_dept WHERE parent_id = #{parentId} AND tenant_id = #{tenantId} AND deleted = 0 " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysDept> selectByParentId(@Param("parentId") Long parentId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据用户ID查询部门列表
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 部门列表
     */
    @Select("SELECT d.* FROM sys_dept d " +
            "INNER JOIN sys_user_dept ud ON d.id = ud.dept_id " +
            "WHERE ud.user_id = #{userId} AND ud.tenant_id = #{tenantId} " +
            "AND d.status = 1 AND d.deleted = 0 " +
            "ORDER BY ud.is_main DESC, d.sort_order ASC, d.create_time ASC")
    List<SysDept> selectDeptsByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据部门ID查询用户列表
     * 
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 用户列表
     */
    @Select("SELECT u.* FROM sys_user u " +
            "INNER JOIN sys_user_dept ud ON u.id = ud.user_id " +
            "WHERE ud.dept_id = #{deptId} AND ud.tenant_id = #{tenantId} " +
            "AND u.status = 1 AND u.deleted = 0 AND ud.deleted = 0 " +
            "ORDER BY ud.is_main DESC, u.create_time ASC")
    List<SysUser> selectUsersByDeptId(@Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    /**
     * 检查部门编码是否存在（排除指定部门ID）
     * 
     * @param deptCode 部门编码
     * @param excludeDeptId 排除的部门ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_dept " +
            "WHERE dept_code = #{deptCode} AND tenant_id = #{tenantId} " +
            "AND deleted = 0 " +
            "AND (#{excludeDeptId} IS NULL OR id != #{excludeDeptId})")
    boolean existsByDeptCode(@Param("deptCode") String deptCode, 
                           @Param("excludeDeptId") Long excludeDeptId, 
                           @Param("tenantId") Long tenantId);
    
    /**
     * 检查部门名称是否存在（同级部门下，排除指定部门ID）
     * 
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeDeptId 排除的部门ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_dept " +
            "WHERE dept_name = #{deptName} AND parent_id = #{parentId} AND tenant_id = #{tenantId} " +
            "AND deleted = 0 " +
            "AND (#{excludeDeptId} IS NULL OR id != #{excludeDeptId})")
    boolean existsByDeptNameAndParentId(@Param("deptName") String deptName, 
                                      @Param("parentId") Long parentId,
                                      @Param("excludeDeptId") Long excludeDeptId, 
                                      @Param("tenantId") Long tenantId);
    
    /**
     * 统计子部门数量
     * 
     * @param parentId 父部门ID
     * @param tenantId 租户ID
     * @return 子部门数量
     */
    @Select("SELECT COUNT(1) FROM sys_dept WHERE parent_id = #{parentId} AND tenant_id = #{tenantId} AND deleted = 0")
    int countByParentId(@Param("parentId") Long parentId, @Param("tenantId") Long tenantId);
    
    /**
     * 统计部门下的用户数量
     * 
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(1) FROM sys_user_dept ud " +
            "INNER JOIN sys_user u ON ud.user_id = u.id " +
            "WHERE ud.dept_id = #{deptId} AND ud.tenant_id = #{tenantId} " +
            "AND u.deleted = 0")
    int countUsersByDeptId(@Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据租户ID查询所有启用的部门
     * 
     * @param tenantId 租户ID
     * @return 部门列表
     */
    @Select("SELECT * FROM sys_dept " +
            "WHERE tenant_id = #{tenantId} AND status = 1 AND deleted = 0 " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysDept> selectEnabledDeptsByTenantId(@Param("tenantId") Long tenantId);
    
    /**
     * 查询部门的所有父级部门ID
     * 
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 父级部门ID列表
     */
    @Select("WITH RECURSIVE dept_tree AS (" +
            "  SELECT id, parent_id, dept_name FROM sys_dept WHERE id = #{deptId} AND tenant_id = #{tenantId} AND deleted = 0" +
            "  UNION ALL" +
            "  SELECT d.id, d.parent_id, d.dept_name FROM sys_dept d " +
            "  INNER JOIN dept_tree dt ON d.id = dt.parent_id WHERE d.tenant_id = #{tenantId} AND d.deleted = 0" +
            ") SELECT id FROM dept_tree WHERE id != #{deptId}")
    List<Long> selectParentIds(@Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    /**
     * 查询部门的所有子级部门ID
     * 
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 子级部门ID列表
     */
    @Select("WITH RECURSIVE dept_tree AS (" +
            "  SELECT id, parent_id, dept_name FROM sys_dept WHERE parent_id = #{deptId} AND tenant_id = #{tenantId} AND deleted = 0" +
            "  UNION ALL" +
            "  SELECT d.id, d.parent_id, d.dept_name FROM sys_dept d " +
            "  INNER JOIN dept_tree dt ON d.parent_id = dt.id WHERE d.tenant_id = #{tenantId} AND d.deleted = 0" +
            ") SELECT id FROM dept_tree")
    List<Long> selectChildIds(@Param("deptId") Long deptId, @Param("tenantId") Long tenantId);

    /**
     * 查询租户下所有部门ID
     *
     * @param tenantId 租户ID
     * @return 部门ID列表
     */
    @Select("SELECT id FROM sys_dept WHERE tenant_id = #{tenantId} AND deleted = 0")
    List<Long> selectAllDeptIdsByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 根据部门ID列表批量查询部门信息（真正的批量查询）
     *
     * @param deptIds 部门ID列表
     * @param tenantId 租户ID
     * @return 部门列表
     */
    @Select("<script>" +
            "SELECT * FROM sys_dept " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0 " +
            "<if test='deptIds != null and deptIds.size() > 0'>" +
            "AND id IN " +
            "<foreach collection='deptIds' item='deptId' open='(' separator=',' close=')'>" +
            "#{deptId}" +
            "</foreach>" +
            "</if>" +
            "ORDER BY sort_order ASC, create_time ASC" +
            "</script>")
    List<SysDept> selectByDeptIds(@Param("deptIds") List<Long> deptIds, @Param("tenantId") Long tenantId);
}

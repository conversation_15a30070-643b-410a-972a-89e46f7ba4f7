package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysOrder;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 佣金结算订单Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysOrderMapper extends BaseMapper<SysOrder> {
    
    /**
     * 批量插入订单
     * 
     * @param orders 订单列表
     * @return 插入成功的记录数
     */
    int insertBatch(@Param("orders") List<SysOrder> orders);
    
    /**
     * 根据父订单ID查询所有子订单
     * 
     * @param parentsPayid 父订单ID
     * @return 子订单列表
     */
    List<SysOrder> selectSubOrdersByParentId(@Param("parentsPayid") String parentsPayid);
    
    /**
     * 查询主订单及其子订单（树形结构）
     * 
     * @param queryWrapper 查询条件
     * @return 主订单列表（包含子订单）
     */
    List<SysOrder> selectMainOrdersWithSubOrders(@Param("ew") QueryWrapper queryWrapper);
    
    /**
     * 分页查询主订单及其子订单
     * 
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<SysOrder> selectMainOrdersWithSubOrdersPage(Page<SysOrder> page, @Param("ew") QueryWrapper queryWrapper);
    
    /**
     * 批量更新订单状态
     * 
     * @param payids 订单ID列表
     * @param orderStatus 订单状态
     * @param settlementStatus 结算状态
     * @return 更新成功的记录数
     */
    int updateStatusBatch(@Param("payids") List<String> payids, 
                         @Param("orderStatus") Integer orderStatus,
                         @Param("settlementStatus") Integer settlementStatus);
    
    /**
     * 根据代理ID查询其所有订单（包括下级主播的订单）
     * 
     * @param agentId 代理ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单列表
     */
    List<SysOrder> selectOrdersByAgent(@Param("agentId") String agentId,
                                      @Param("startTime") Long startTime,
                                      @Param("endTime") Long endTime);
    
    /**
     * 根据主播ID查询其订单
     * 
     * @param anchorId 主播ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单列表
     */
    List<SysOrder> selectOrdersByAnchor(@Param("anchorId") String anchorId,
                                       @Param("startTime") Long startTime,
                                       @Param("endTime") Long endTime);
    
    /**
     * 统计指定时间范围内的订单数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态（可选）
     * @return 订单数量
     */
    Long countOrdersByTimeRange(@Param("startTime") Long startTime,
                               @Param("endTime") Long endTime,
                               @Param("orderStatus") Integer orderStatus);
    
    /**
     * 查询指定用户在指定时间范围内是否已有订单
     * 
     * @param userId 用户ID（代理或主播）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否存在订单
     */
    boolean existsOrderByUserAndTimeRange(@Param("userId") String userId,
                                         @Param("startTime") Long startTime,
                                         @Param("endTime") Long endTime);
}

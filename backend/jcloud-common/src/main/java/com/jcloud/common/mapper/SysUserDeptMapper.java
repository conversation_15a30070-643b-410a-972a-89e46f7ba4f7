package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysUserDept;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;



/**
 * 用户部门关联Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysUserDeptMapper extends BaseMapper<SysUserDept> {
    
    /**
     * 根据用户ID查询部门关联
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 部门关联列表
     */
    @Select("SELECT * FROM sys_user_dept WHERE user_id = #{userId} AND tenant_id = #{tenantId}")
    List<SysUserDept> selectByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据部门ID查询用户关联
     * 
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 用户关联列表
     */
    @Select("SELECT * FROM sys_user_dept WHERE dept_id = #{deptId} AND tenant_id = #{tenantId}")
    List<SysUserDept> selectByDeptId(@Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据用户ID和部门ID查询关联
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 关联信息
     */
    @Select("SELECT * FROM sys_user_dept WHERE user_id = #{userId} AND dept_id = #{deptId} AND tenant_id = #{tenantId}")
    SysUserDept selectByUserIdAndDeptId(@Param("userId") Long userId, @Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据用户ID删除部门关联
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM sys_user_dept WHERE user_id = #{userId} AND tenant_id = #{tenantId}")
    int deleteByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据部门ID删除用户关联
     * 
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM sys_user_dept WHERE dept_id = #{deptId} AND tenant_id = #{tenantId}")
    int deleteByDeptId(@Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据用户ID和部门ID删除关联
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM sys_user_dept WHERE user_id = #{userId} AND dept_id = #{deptId} AND tenant_id = #{tenantId}")
    int deleteByUserIdAndDeptId(@Param("userId") Long userId, @Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    // 移除自定义的insertBatch方法，使用MyBatis-Flex BaseMapper提供的原生批量插入方法
    // BaseMapper已经提供了insertBatch(Collection<T> entities)方法
    
    /**
     * 获取用户的主部门ID
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 主部门ID
     */
    @Select("SELECT dept_id FROM sys_user_dept WHERE user_id = #{userId} AND tenant_id = #{tenantId} AND is_main = 1 AND deleted = 0")
    Long selectPrimaryDeptIdByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 设置用户主部门
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 更新数量
     */
    @Update("UPDATE sys_user_dept SET is_main = CASE WHEN dept_id = #{deptId} THEN 1 ELSE 0 END " +
            "WHERE user_id = #{userId} AND tenant_id = #{tenantId} AND deleted = 0")
    int updatePrimaryDept(@Param("userId") Long userId, @Param("deptId") Long deptId, @Param("tenantId") Long tenantId);

    /**
     * 根据用户ID查询所属部门ID列表
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 部门ID列表
     */
    @Select("SELECT dept_id FROM sys_user_dept WHERE user_id = #{userId} AND tenant_id = #{tenantId} AND deleted = 0")
    List<Long> selectDeptIdsByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 根据部门ID列表查询用户ID列表
     *
     * @param deptIds 部门ID列表
     * @param tenantId 租户ID
     * @return 用户ID列表
     */
    @Select("<script>" +
            "SELECT DISTINCT user_id FROM sys_user_dept " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0 " +
            "<if test='deptIds != null and deptIds.size() > 0'>" +
            "AND dept_id IN " +
            "<foreach collection='deptIds' item='deptId' open='(' separator=',' close=')'>" +
            "#{deptId}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<Long> selectUserIdsByDeptIds(@Param("deptIds") List<Long> deptIds, @Param("tenantId") Long tenantId);

    /**
     * 查询租户下所有用户ID
     *
     * @param tenantId 租户ID
     * @return 用户ID列表
     */
    @Select("SELECT DISTINCT user_id FROM sys_user_dept WHERE tenant_id = #{tenantId} AND deleted = 0")
    List<Long> selectAllUserIdsByTenantId(@Param("tenantId") Long tenantId);

    // selectPrimaryDeptIdByUserId方法已在上面定义，移除重复定义

    /**
     * 根据用户ID查询第一个部门ID（如果没有主部门）
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 部门ID
     */
    @Select("SELECT dept_id FROM sys_user_dept WHERE user_id = #{userId} AND tenant_id = #{tenantId} AND deleted = 0 ORDER BY is_main DESC, id ASC LIMIT 1")
    Long selectFirstDeptIdByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 检查用户是否在指定部门中
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 是否存在关联
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_user_dept WHERE user_id = #{userId} AND dept_id = #{deptId} AND tenant_id = #{tenantId} AND deleted = 0")
    boolean existsByUserIdAndDeptId(@Param("userId") Long userId, @Param("deptId") Long deptId, @Param("tenantId") Long tenantId);

    /**
     * 批量检查用户部门关联是否已存在
     *
     * @param userIds 用户ID列表
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 已存在关联的用户ID列表
     */
    @Select("<script>" +
            "SELECT DISTINCT user_id FROM sys_user_dept " +
            "WHERE dept_id = #{deptId} AND tenant_id = #{tenantId} AND deleted = 0 " +
            "<if test='userIds != null and userIds.size() > 0'>" +
            "AND user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<Long> selectExistingUserDeptRelations(@Param("userIds") List<Long> userIds, @Param("deptId") Long deptId, @Param("tenantId") Long tenantId);

    /**
     * 批量查询用户的主部门ID
     *
     * @param userIds 用户ID列表
     * @param tenantId 租户ID
     * @return 用户ID和部门ID的映射
     */
    @Select("<script>" +
            "SELECT user_id, dept_id FROM sys_user_dept " +
            "WHERE tenant_id = #{tenantId} AND is_main = 1 AND deleted = 0 " +
            "<if test='userIds != null and userIds.size() > 0'>" +
            "AND user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    @Results({
        @Result(column = "user_id", property = "userId"),
        @Result(column = "dept_id", property = "deptId")
    })
    List<SysUserDept> selectPrimaryDeptsByUserIds(@Param("userIds") List<Long> userIds, @Param("tenantId") Long tenantId);

    /**
     * 批量查询用户的所有部门关联（真正的批量查询）
     *
     * @param userIds 用户ID列表
     * @param tenantId 租户ID
     * @return 用户部门关联列表
     */
    @Select("<script>" +
            "SELECT user_id, dept_id, is_main FROM sys_user_dept " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0 " +
            "<if test='userIds != null and userIds.size() > 0'>" +
            "AND user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</if>" +
            "ORDER BY user_id, is_main DESC" +
            "</script>")
    @Results({
        @Result(column = "user_id", property = "userId"),
        @Result(column = "dept_id", property = "deptId"),
        @Result(column = "is_main", property = "isMain")
    })
    List<SysUserDept> selectAllDeptsByUserIds(@Param("userIds") List<Long> userIds, @Param("tenantId") Long tenantId);

    // deleteByUserIdAndDeptId方法已在上面定义，移除重复定义
}

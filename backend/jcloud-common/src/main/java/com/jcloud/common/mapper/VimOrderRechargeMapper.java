package com.jcloud.common.mapper;

import com.jcloud.common.entity.VimOrderRecharge;
import com.mybatisflex.annotation.UseDataSource;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * VimOrderRecharge Mapper接口（从库查询）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
@UseDataSource("slave")
public interface VimOrderRechargeMapper extends BaseMapper<VimOrderRecharge> {
    
    /**
     * 查询指定用户在指定时间范围内的成功充值记录
     * 
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值记录列表
     */
    @Select("SELECT * FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime} ORDER BY create_time DESC")
    List<VimOrderRecharge> selectSuccessRechargesByUser(@Param("uid") Integer uid,
                                                       @Param("startTime") Integer startTime,
                                                       @Param("endTime") Integer endTime);
    
    /**
     * 查询指定用户列表在指定时间范围内的成功充值记录
     * 
     * @param uids 用户ID列表
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值记录列表
     */
    List<VimOrderRecharge> selectSuccessRechargesByUsers(@Param("uids") List<Integer> uids,
                                                        @Param("startTime") Integer startTime,
                                                        @Param("endTime") Integer endTime);
    
    /**
     * 统计指定用户在指定时间范围内的充值总金额
     * 
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值总金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    BigDecimal sumRechargeAmountByUser(@Param("uid") Integer uid,
                                      @Param("startTime") Integer startTime,
                                      @Param("endTime") Integer endTime);
    
    /**
     * 统计指定用户列表在指定时间范围内的充值总金额
     * 
     * @param uids 用户ID列表
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值总金额
     */
    BigDecimal sumRechargeAmountByUsers(@Param("uids") List<Integer> uids,
                                       @Param("startTime") Integer startTime,
                                       @Param("endTime") Integer endTime);
    
    /**
     * 统计指定用户在指定时间范围内的充值次数
     * 
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值次数
     */
    @Select("SELECT COUNT(*) FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Long countRechargesByUser(@Param("uid") Integer uid,
                             @Param("startTime") Integer startTime,
                             @Param("endTime") Integer endTime);
    
    /**
     * 查询指定时间范围内的所有成功充值记录
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值记录列表
     */
    @Select("SELECT * FROM vim_order_recharge WHERE state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime} ORDER BY create_time DESC")
    List<VimOrderRecharge> selectSuccessRechargesByTimeRange(@Param("startTime") Integer startTime,
                                                            @Param("endTime") Integer endTime);
    
    /**
     * 查询用户的最近一次成功充值记录
     * 
     * @param uid 用户ID
     * @return 最近充值记录
     */
    @Select("SELECT * FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 ORDER BY create_time DESC LIMIT 1")
    VimOrderRecharge selectLatestSuccessRechargeByUser(@Param("uid") Integer uid);
    
    /**
     * 查询指定用户的首次充值记录
     * 
     * @param uid 用户ID
     * @return 首次充值记录
     */
    @Select("SELECT * FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 ORDER BY create_time ASC LIMIT 1")
    VimOrderRecharge selectFirstRechargeByUser(@Param("uid") Integer uid);
    
    /**
     * 统计指定时间范围内的充值用户数
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 充值用户数
     */
    @Select("SELECT COUNT(DISTINCT uid) FROM vim_order_recharge WHERE state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Long countRechargeUsersByTimeRange(@Param("startTime") Integer startTime,
                                      @Param("endTime") Integer endTime);
    
    /**
     * 查询指定用户是否在指定时间范围内有充值记录
     * 
     * @param uid 用户ID
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 是否有充值记录
     */
    @Select("SELECT COUNT(*) > 0 FROM vim_order_recharge WHERE uid = #{uid} AND state = 2 AND create_time >= #{startTime} AND create_time <= #{endTime}")
    boolean existsRechargeByUserAndTimeRange(@Param("uid") Integer uid,
                                           @Param("startTime") Integer startTime,
                                           @Param("endTime") Integer endTime);
}

package com.jcloud.common.mapper;

import com.jcloud.common.entity.VimUser;
import com.mybatisflex.annotation.UseDataSource;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * VimUser Mapper接口
 * 用于查询从库中的主播用户数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
@UseDataSource("slave") // 使用MyBatis-Flex官方多数据源注解
public interface VimUserMapper extends BaseMapper<VimUser> {
    
    /**
     * 查询所有主播用户（identity = 2 或 3）
     *
     * @return 主播用户列表
     */
    @Select("SELECT * FROM vim_user WHERE identity IN (2, 3) AND state = 1 ORDER BY create_time DESC")
    List<VimUser> selectAnchorUsers();

    /**
     * 查询所有代理用户（identity = 4）
     *
     * @return 代理用户列表
     */
    @Select("SELECT * FROM vim_user WHERE identity = 4 AND state = 1 ORDER BY create_time DESC")
    List<VimUser> selectAgentUsers();

    /**
     * 查询所有用户（identity = 2, 3, 4）
     *
     * @return 用户列表
     */
    @Select("SELECT * FROM vim_user WHERE state = 1 and id > 200 ORDER BY create_time DESC")
    List<VimUser> selectAnchorAndAgentUsers();
    
    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM vim_user WHERE phone = #{phone}")
    VimUser selectByPhone(String phone);
    
    /**
     * 统计主播用户总数
     *
     * @return 主播用户总数
     */
    @Select("SELECT COUNT(*) FROM vim_user WHERE identity IN (2, 3) AND state = 1")
    Long countAnchorUsers();

    /**
     * 统计代理用户总数
     *
     * @return 代理用户总数
     */
    @Select("SELECT COUNT(*) FROM vim_user WHERE identity = 4 AND state = 1")
    Long countAgentUsers();

    /**
     * 统计用户总数
     *
     * @return 用户总数
     */
    @Select("SELECT COUNT(*) FROM vim_user WHERE identity IN (2, 3, 4) AND state = 1")
    Long countAnchorAndAgentUsers();

    /**
     * 根据邀请用户ID查询下级用户
     *
     * @param inviteUserId 邀请用户ID
     * @return 下级用户列表
     */
    @Select("SELECT * FROM vim_user WHERE invite_user = #{inviteUserId} AND state = 1")
    List<VimUser> selectUsersByInviteUser(@Param("inviteUserId") Integer inviteUserId);

    /**
     * 根据用户ID查询用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @Select("SELECT * FROM vim_user WHERE id = #{userId}")
    VimUser selectByUserId(@Param("userId") Integer userId);

    /**
     * 递归查询用户的所有下级用户（多级）
     * 使用XML实现复杂的递归查询
     * @param userId 用户ID
     * @return 所有下级用户列表
     */
    List<VimUser> selectAllSubUsersRecursive(@Param("userId") Integer userId);

    /**
     * 查询用户的上级链路（向上追溯）
     * 使用XML实现复杂的递归查询
     *
     * @param userId 用户ID
     * @return 上级用户链路列表（从直接上级到顶级）
     */
    List<VimUser> selectParentChain(@Param("userId") Integer userId);

    /**
     * 查询指定代理下的所有主播
     *
     * @param agentId 代理ID
     * @return 主播列表
     */
    @Select("SELECT * FROM vim_user WHERE invite_user = #{agentId} AND identity IN (2, 3) AND state = 1")
    List<VimUser> selectAnchorsByAgent(@Param("agentId") Integer agentId);

    /**
     * 查询指定主播的代理
     *
     * @param anchorId 主播ID
     * @return 代理信息
     */
    @Select("SELECT u2.* FROM vim_user u1 JOIN vim_user u2 ON u1.invite_user = u2.id WHERE u1.id = #{anchorId} AND u2.identity = 4")
    VimUser selectAgentByAnchor(@Param("anchorId") Integer anchorId);

    /**
     * 批量查询用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    List<VimUser> selectByUserIds(@Param("userIds") List<Integer> userIds);

    /**
     * 统计指定代理下的主播数量
     *
     * @param agentId 代理ID
     * @return 主播数量
     */
    @Select("SELECT COUNT(*) FROM vim_user WHERE invite_user = #{agentId} AND identity IN (2, 3) AND state = 1")
    Long countAnchorsByAgent(@Param("agentId") Integer agentId);
}

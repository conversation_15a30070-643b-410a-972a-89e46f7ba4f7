package com.jcloud.common.result;

import lombok.Getter;

/**
 * 响应状态码枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数验证失败"),
    
    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 1xxx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    USER_LOCKED(1003, "用户已被锁定"),
    PASSWORD_ERROR(1004, "密码错误"),
    PASSWORD_EXPIRED(1005, "密码已过期"),
    
    // 认证授权错误 2xxx
    TOKEN_INVALID(2001, "令牌无效"),
    TOKEN_EXPIRED(2002, "令牌已过期"),
    PERMISSION_DENIED(2003, "权限不足"),
    ROLE_NOT_FOUND(2004, "角色不存在"),
    
    // 租户相关错误 3xxx
    TENANT_NOT_FOUND(3001, "租户不存在"),
    TENANT_DISABLED(3002, "租户已被禁用"),
    TENANT_EXPIRED(3003, "租户已过期"),
    
    // 数据操作错误 4xxx
    DATA_NOT_FOUND(4001, "数据不存在"),
    DATA_ALREADY_EXISTS(4002, "数据已存在"),
    DATA_INTEGRITY_VIOLATION(4003, "数据完整性约束违反"),
    BATCH_OPERATION_FAILED(4004, "批量操作失败"),
    
    // 文件操作错误 5xxx
    FILE_NOT_FOUND(5001, "文件不存在"),
    FILE_UPLOAD_ERROR(5002, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(5003, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(5004, "文件大小超出限制");
    
    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;

    /**
     * 构造函数
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}

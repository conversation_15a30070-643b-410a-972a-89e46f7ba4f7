package com.jcloud.common.service;
import com.jcloud.common.entity.BaseEntity;
import com.jcloud.common.page.PageQuery;
import com.jcloud.common.page.PageResult;

import java.io.Serializable;
import java.util.List;

/**
 * 基础服务接口
 * 定义通用的CRUD操作，支持多租户数据隔离
 * 
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 1.0.0
 */
public interface BaseService<T extends BaseEntity> {
    
    /**
     * 根据ID查询实体（自动过滤租户）
     *
     * @param id 主键ID
     * @return 实体对象
     */
    T getById(Serializable id);
    
    /**
     * 根据ID查询实体（不过滤租户，仅超级管理员可用）
     * 
     * @param id 主键ID
     * @return 实体对象
     */
    T getByIdWithoutTenant(Serializable id);
    
    /**
     * 查询所有实体（自动过滤租户）
     * 
     * @return 实体列表
     */
    List<T> list();
    
    /**
     * 根据条件查询实体列表（自动过滤租户）
     * 
     * @param entity 查询条件
     * @return 实体列表
     */
    List<T> list(T entity);
    
    /**
     * 分页查询（自动过滤租户）
     * 
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    PageResult<T> page(PageQuery pageQuery);
    
    /**
     * 根据条件分页查询（自动过滤租户）
     * 
     * @param pageQuery 分页参数
     * @param entity 查询条件
     * @return 分页结果
     */
    PageResult<T> page(PageQuery pageQuery, T entity);
    
    /**
     * 保存实体（新增或更新）
     * 自动设置租户ID、创建人、更新人等信息
     * 
     * @param entity 实体对象
     * @return 是否成功
     */
    boolean save(T entity);
    
    /**
     * 批量保存实体
     * 
     * @param entityList 实体列表
     * @return 是否成功
     */
    boolean saveBatch(List<T> entityList);
    
    /**
     * 根据ID更新实体（自动过滤租户）
     * 
     * @param entity 实体对象
     * @return 是否成功
     */
    boolean updateById(T entity);
    
    /**
     * 批量更新实体
     * 
     * @param entityList 实体列表
     * @return 是否成功
     */
    boolean updateBatchById(List<T> entityList);
    
    /**
     * 根据ID删除实体（逻辑删除，自动过滤租户）
     * 
     * @param id 主键ID
     * @return 是否成功
     */
    boolean removeById(Serializable id);
    
    /**
     * 根据ID列表批量删除实体（逻辑删除，自动过滤租户）
     * 
     * @param idList 主键ID列表
     * @return 是否成功
     */
    boolean removeByIds(List<? extends Serializable> idList);
    
    /**
     * 根据条件删除实体（逻辑删除，自动过滤租户）
     * 
     * @param entity 删除条件
     * @return 是否成功
     */
    boolean remove(T entity);
    
    /**
     * 根据ID物理删除实体（谨慎使用）
     * 
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deleteById(Serializable id);
    
    /**
     * 统计记录数（自动过滤租户）
     * 
     * @return 记录数
     */
    long count();
    
    /**
     * 根据条件统计记录数（自动过滤租户）
     * 
     * @param entity 统计条件
     * @return 记录数
     */
    long count(T entity);
    
    /**
     * 检查实体是否存在（自动过滤租户）
     * 
     * @param id 主键ID
     * @return 是否存在
     */
    boolean exists(Serializable id);
    
    /**
     * 根据条件检查实体是否存在（自动过滤租户）
     * 
     * @param entity 检查条件
     * @return 是否存在
     */
    boolean exists(T entity);
}

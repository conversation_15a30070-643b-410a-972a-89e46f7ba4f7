package com.jcloud.common.util;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * 角色编码解析工具类
 * 用于解析代理角色编码，提取查询层数限制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class RoleCodeUtils {

    /**
     * 主播角色编码
     */
    private static final String ANCHOR_ROLE = "ANCHOR";

    /**
     * 代理角色编码
     */
    private static final String AGENT_ROLE = "AGENT";

    /**
     * 主播角色查询层数
     */
    private static final int ANCHOR_QUERY_LEVEL = 1;

    /**
     * 代理角色查询层数
     */
    private static final int AGENT_QUERY_LEVEL = 2;

    /**
     * 默认查询层数
     */
    private static final int DEFAULT_QUERY_LEVEL = 0;
    
    /**
     * 最大查询层数限制
     */
    private static final int MAX_QUERY_LEVEL = 0;
    
    /**
     * 最小查询层数限制
     */
    private static final int MIN_QUERY_LEVEL = 1;
    
    /**
     * 从角色编码集合中解析查询层数
     * 
     * @param roleCodes 角色编码集合
     * @return 查询层数（1-10之间的整数）
     */
    public static int parseQueryLevel(Set<String> roleCodes) {
        if (CollUtil.isEmpty(roleCodes)) {
            log.debug("角色编码集合为空，使用默认查询层数：{}", DEFAULT_QUERY_LEVEL);
            return DEFAULT_QUERY_LEVEL;
        }

        int maxLevel = DEFAULT_QUERY_LEVEL;

        // 检查是否包含代理角色（优先级更高）
        if (roleCodes.contains(AGENT_ROLE)) {
            maxLevel = AGENT_QUERY_LEVEL;
            log.debug("找到代理角色：{}，层数：{}", AGENT_ROLE, AGENT_QUERY_LEVEL);
        }
        // 检查是否包含主播角色
        else if (roleCodes.contains(ANCHOR_ROLE)) {
            maxLevel = ANCHOR_QUERY_LEVEL;
            log.debug("找到主播角色：{}，层数：{}", ANCHOR_ROLE, ANCHOR_QUERY_LEVEL);
        }

        log.debug("用户查询层数解析完成，角色：{}，查询层数：{}", roleCodes, maxLevel);
        return maxLevel;
    }
    
    /**
     * 获取默认查询层数
     * 
     * @return 默认查询层数
     */
    public static int getDefaultQueryLevel() {
        return DEFAULT_QUERY_LEVEL;
    }
    
    /**
     * 获取最大查询层数
     * 
     * @return 最大查询层数
     */
    public static int getMaxQueryLevel() {
        return MAX_QUERY_LEVEL;
    }
    
    /**
     * 获取最小查询层数
     * 
     * @return 最小查询层数
     */
    public static int getMinQueryLevel() {
        return MIN_QUERY_LEVEL;
    }
}

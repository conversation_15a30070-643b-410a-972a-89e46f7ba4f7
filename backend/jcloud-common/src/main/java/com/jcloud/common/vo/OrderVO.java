package com.jcloud.common.vo;

import com.jcloud.common.enums.OrderStatus;
import com.jcloud.common.enums.SettlementStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单展示VO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "订单展示信息")
public class OrderVO {
    
    /**
     * 结算订单ID
     */
    @Schema(description = "结算订单ID")
    private String payid;
    
    /**
     * 上级关联订单ID
     */
    @Schema(description = "上级关联订单ID")
    private String parentsPayid;
    
    /**
     * 代理ID
     */
    @Schema(description = "代理ID")
    private String agent;
    
    /**
     * 代理昵称
     */
    @Schema(description = "代理昵称")
    private String agentNickname;
    
    /**
     * 主播ID
     */
    @Schema(description = "主播ID")
    private String anchor;
    
    /**
     * 主播昵称
     */
    @Schema(description = "主播昵称")
    private String anchorNickname;
    
    /**
     * 自充值金额
     */
    @Schema(description = "自充值金额")
    private BigDecimal amountSelf;
    
    /**
     * 推广充值金额
     */
    @Schema(description = "推广充值金额")
    private BigDecimal amountFans;
    
    /**
     * 总充值金额
     */
    @Schema(description = "总充值金额")
    private BigDecimal totalAmount;
    
    /**
     * 劳务比例
     */
    @Schema(description = "劳务比例")
    private BigDecimal feeRate;
    
    /**
     * 推广劳务费
     */
    @Schema(description = "推广劳务费")
    private BigDecimal feeValue;
    
    /**
     * 实际结算金额
     */
    @Schema(description = "实际结算金额")
    private BigDecimal feeActual;
    
    /**
     * 收款人
     */
    @Schema(description = "收款人")
    private String feePerson;
    
    /**
     * 收款账号
     */
    @Schema(description = "收款账号")
    private String feeAccount;
    
    /**
     * 订单状态代码
     */
    @Schema(description = "订单状态代码")
    private Integer orderStatus;
    
    /**
     * 订单状态描述
     */
    @Schema(description = "订单状态描述")
    private String orderStatusDesc;
    
    /**
     * 结算状态代码
     */
    @Schema(description = "结算状态代码")
    private Integer settlementStatus;
    
    /**
     * 结算状态描述
     */
    @Schema(description = "结算状态描述")
    private String settlementStatusDesc;

    /**
     * 结算开始时间（时间戳，秒）
     */
    @Schema(description = "结算开始时间")
    private Long settlementStartTime;

    /**
     * 结算结束时间（时间戳，秒）
     */
    @Schema(description = "结算结束时间")
    private Long settlementEndTime;

    /**
     * 创建时间（时间戳，秒）
     */
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 更新时间（时间戳，秒）
     */
    @Schema(description = "更新时间")
    private Long updateTime;
    
    /**
     * 是否为主订单
     */
    @Schema(description = "是否为主订单")
    private Boolean isMainOrder;
    
    /**
     * 子订单列表（仅主订单有）
     */
    @Schema(description = "子订单列表")
    private List<OrderVO> subOrders;
    
    // ==================== 业务方法 ====================
    
    /**
     * 设置订单状态枚举并自动设置描述
     */
    public void setOrderStatusEnum(OrderStatus orderStatus) {
        if (orderStatus != null) {
            this.orderStatus = orderStatus.getCode();
            this.orderStatusDesc = orderStatus.getDescription();
        }
    }
    
    /**
     * 设置结算状态枚举并自动设置描述
     */
    public void setSettlementStatusEnum(SettlementStatus settlementStatus) {
        if (settlementStatus != null) {
            this.settlementStatus = settlementStatus.getCode();
            this.settlementStatusDesc = settlementStatus.getDescription();
        }
    }
    
    /**
     * 判断是否有子订单
     */
    public boolean hasSubOrders() {
        return subOrders != null && !subOrders.isEmpty();
    }
    
    /**
     * 获取子订单数量
     */
    public int getSubOrderCount() {
        return hasSubOrders() ? subOrders.size() : 0;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.VimOrderRechargeMapper">

    <!-- 查询指定用户列表在指定时间范围内的成功充值记录 -->
    <select id="selectSuccessRechargesByUsers" resultType="com.jcloud.common.entity.VimOrderRecharge">
        SELECT * FROM vim_order_recharge 
        WHERE uid IN
        <foreach collection="uids" item="uid" open="(" separator="," close=")">
            #{uid}
        </foreach>
        AND state = 2 
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
        ORDER BY uid, create_time DESC
    </select>

    <!-- 统计指定用户列表在指定时间范围内的充值总金额 -->
    <select id="sumRechargeAmountByUsers" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0) FROM vim_order_recharge 
        WHERE uid IN
        <foreach collection="uids" item="uid" open="(" separator="," close=")">
            #{uid}
        </foreach>
        AND state = 2 
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
    </select>

    <!-- 批量查询用户信息 -->
    <select id="selectByUserIds" resultType="com.jcloud.common.entity.VimUser">
        SELECT * FROM vim_user 
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND state = 1
        ORDER BY id
    </select>

</mapper>

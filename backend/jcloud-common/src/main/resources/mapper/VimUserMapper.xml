<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.VimUserMapper">

    <!-- 递归查询用户的所有下级用户（多级） -->
    <select id="selectAllSubUsersRecursive" resultType="com.jcloud.common.entity.VimUser">
        WITH RECURSIVE user_hierarchy AS (
            -- 基础查询：直接下级
            SELECT id, phone, nickname, username, userimage, invite_user, identity, state, 1 as level
            FROM vim_user 
            WHERE invite_user = #{userId} AND state = 1
            
            UNION ALL
            
            -- 递归查询：下级的下级
            SELECT u.id, u.phone, u.nickname, u.username, u.userimage, u.invite_user, u.identity, u.state, uh.level + 1
            FROM vim_user u
            INNER JOIN user_hierarchy uh ON u.invite_user = uh.id
            WHERE u.state = 1 AND uh.level &lt; 10  -- 限制递归深度防止无限循环
        )
        SELECT * FROM user_hierarchy ORDER BY level, id
    </select>

    <!-- 查询用户的上级链路（向上追溯） -->
    <select id="selectParentChain" resultType="com.jcloud.common.entity.VimUser">
        WITH RECURSIVE parent_chain AS (
            -- 基础查询：当前用户
            SELECT u.id, u.phone, u.nickname, u.username, u.userimage, u.invite_user, u.identity, u.state, 0 as level
            FROM vim_user u
            WHERE u.id = #{userId}
            
            UNION ALL
            
            -- 递归查询：上级用户
            SELECT u.id, u.phone, u.nickname, u.username, u.userimage, u.invite_user, u.identity, u.state, pc.level + 1
            FROM vim_user u
            INNER JOIN parent_chain pc ON u.id = pc.invite_user
            WHERE u.state = 1 AND pc.level &lt; 10  -- 限制递归深度
        )
        SELECT * FROM parent_chain WHERE level > 0 ORDER BY level
    </select>

    <!-- 批量查询用户信息 -->
    <select id="selectByUserIds" resultType="com.jcloud.common.entity.VimUser">
        SELECT * FROM vim_user 
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND state = 1
        ORDER BY id
    </select>

    <!-- 查询指定用户列表在指定时间范围内的成功充值记录 -->
    <select id="selectSuccessRechargesByUsers" resultType="com.jcloud.common.entity.VimOrderRecharge">
        SELECT * FROM vim_order_recharge 
        WHERE uid IN
        <foreach collection="uids" item="uid" open="(" separator="," close=")">
            #{uid}
        </foreach>
        AND state = 2 
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
        ORDER BY uid, create_time DESC
    </select>

    <!-- 统计指定用户列表在指定时间范围内的充值总金额 -->
    <select id="sumRechargeAmountByUsers" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0) FROM vim_order_recharge 
        WHERE uid IN
        <foreach collection="uids" item="uid" open="(" separator="," close=")">
            #{uid}
        </foreach>
        AND state = 2 
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
    </select>

</mapper>

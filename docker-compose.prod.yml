# jCloud生产环境Docker Compose配置文件
# 使用外部数据库和Redis，仅部署应用服务
version: '3.8'

# 网络配置
networks:
  jcloud-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  backend-logs:
    driver: local
  backend-uploads:
    driver: local

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: jcloud/backend:1.0.0
    container_name: jcloud-backend
    restart: unless-stopped
    environment:
      # 数据库配置 - 使用外部生产数据库
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_MASTER_NAME: ${DB_MASTER_NAME}
      DB_SLAVE_NAME: ${DB_SLAVE_NAME}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_SSL: true
      
      # Redis配置 - 使用外部生产Redis
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      REDIS_DATABASE: ${REDIS_DATABASE}
      
      # 应用配置
      SERVER_PORT: 8081
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      LOG_PATH: ${LOG_PATH}
      FILE_UPLOAD_PATH: ${FILE_UPLOAD_PATH}
      
      # JVM配置
      JAVA_OPTS: ${JAVA_OPTS}
      
      # 时区
      TZ: ${TZ}
    ports:
      - "${BACKEND_PORT}:8081"
    volumes:
      - backend-logs:${LOG_PATH}
      - backend-uploads:${FILE_UPLOAD_PATH}
    networks:
      jcloud-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/api/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 60s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NGINX_CONFIG=nginx-prod.conf
    image: jcloud/frontend:1.0.0
    container_name: jcloud-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      TZ: ${TZ}
    ports:
      - "${FRONTEND_PORT}:80"
    networks:
      jcloud-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      timeout: 10s
      retries: 3
      start_period: 30s

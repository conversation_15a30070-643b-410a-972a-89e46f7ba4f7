# jCloud前端Dockerfile
# 使用多阶段构建优化镜像大小

# 构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm@latest

# 复制package文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm run build

# 生产阶段 - 使用Nginx提供静态文件服务
FROM nginx:1.25-alpine

# 安装必要的工具
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 从构建阶段复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置文件（支持通过构建参数选择配置文件）
ARG NGINX_CONFIG=nginx.conf
COPY ${NGINX_CONFIG} /etc/nginx/nginx.conf

# 创建Nginx运行用户
RUN addgroup -g 1001 -S nginx-app && \
    adduser -S nginx-app -u 1001 -G nginx-app

# 设置权限
RUN chown -R nginx-app:nginx-app /usr/share/nginx/html && \
    chown -R nginx-app:nginx-app /var/cache/nginx && \
    chown -R nginx-app:nginx-app /var/log/nginx && \
    chown -R nginx-app:nginx-app /etc/nginx/conf.d

# 创建nginx.pid文件目录并设置权限
RUN touch /var/run/nginx.pid && \
    chown nginx-app:nginx-app /var/run/nginx.pid

# 切换到非root用户
USER nginx-app

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]

# 元数据标签
LABEL maintainer="jCloud Team <<EMAIL>>" \
      version="1.0.0" \
      description="jCloud权限管理系统前端服务" \
      org.opencontainers.image.title="jCloud Frontend" \
      org.opencontainers.image.description="jCloud权限管理系统前端服务" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="jCloud" \
      org.opencontainers.image.licenses="MIT"

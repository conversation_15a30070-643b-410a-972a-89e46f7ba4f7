import React from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/Button'
import { ShieldAlert, Users, X } from 'lucide-react'
import { useAuthStore } from '@/stores/auth'
import { toast } from '@/hooks/useToast'

/**
 * 模拟登录状态Banner组件
 * 在用户处于模拟登录状态时显示在页面顶部
 */
export const ImpersonationBanner: React.FC = () => {
  const {
    impersonating,
    impersonatorUsername,
    user,
    impersonationReason,
    impersonationStartTime,
    stopImpersonation,
    loading
  } = useAuthStore()

  // 如果不在模拟状态，不显示Banner
  if (!impersonating) {
    return null
  }

  const handleStopImpersonation = async () => {
    try {
      await stopImpersonation()
      toast({
        title: '成功',
        description: '已成功退出模拟登录状态',
        variant: 'success'
      })
    } catch (error) {
      console.error('退出模拟登录失败:', error)
      toast({
        title: '错误',
        description: '退出模拟登录失败，请重试',
        variant: 'destructive'
      })
    }
  }

  const formatStartTime = (timeStr: string | null) => {
    if (!timeStr) return ''
    try {
      // 处理秒级时间戳：将字符串转换为数字，然后乘以1000转换为毫秒级时间戳
      const timestamp = parseInt(timeStr, 10)
      if (isNaN(timestamp)) {
        // 如果不是数字，尝试直接解析为日期
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      }

      // 秒级时间戳转换为毫秒级时间戳
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return timeStr
    }
  }

  return (
    <Alert className="border-orange-200 bg-orange-50 text-orange-800 rounded-none border-b-2 border-t-0 border-l-0 border-r-0">
      <ShieldAlert className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between w-full">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="font-medium">
              模拟登录状态：您正在以 "{user?.username || '未知用户'}" 的身份浏览系统
            </span>
          </div>
          
          <div className="text-sm text-orange-600 space-x-4">
            {impersonatorUsername && (
              <span>管理员：{impersonatorUsername}</span>
            )}
            {impersonationStartTime && (
              <span>开始时间：{formatStartTime(impersonationStartTime)}</span>
            )}
            {impersonationReason && (
              <span>原因：{impersonationReason}</span>
            )}
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={handleStopImpersonation}
          disabled={loading}
          className="bg-white hover:bg-orange-100 border-orange-300 text-orange-800 hover:text-orange-900"
        >
          <X className="h-3 w-3 mr-1" />
          退出模拟
        </Button>
      </AlertDescription>
    </Alert>
  )
}

export default ImpersonationBanner

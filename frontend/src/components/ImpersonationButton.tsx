import React, { useState } from 'react'
import { Button } from '@/components/ui/Button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Users, AlertTriangle } from 'lucide-react'
import { useAuthStore } from '@/stores/auth'
import { usePermission } from '@/stores/auth'
import { toast } from '@/hooks/useToast'

interface ImpersonationButtonProps {
  targetUserId: number
  targetUsername: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  className?: string
}

/**
 * 模拟登录按钮组件
 * 仅对超级管理员可见，点击后可以模拟登录到指定用户
 */
export const ImpersonationButton: React.FC<ImpersonationButtonProps> = ({
  targetUserId,
  targetUsername,
  variant = 'outline',
  size = 'sm',
  className = ''
}) => {
  const [open, setOpen] = useState(false)
  const [reason, setReason] = useState('')
  
  const { startImpersonation, loading, impersonating } = useAuthStore()
  const { isSuperAdmin, hasPermission } = usePermission()

  // 检查权限：必须是超级管理员且有模拟登录权限
  const canImpersonate = isSuperAdmin() && hasPermission('user:impersonate:start')

  // 如果没有权限或已经在模拟状态，不显示按钮
  if (!canImpersonate || impersonating) {
    return null
  }

  const handleSubmit = async () => {
    // 验证输入
    if (!reason.trim()) {
      toast({
        title: '错误',
        description: '请输入模拟登录原因',
        variant: 'destructive'
      })
      return
    }

    if (reason.trim().length < 5) {
      toast({
        title: '错误',
        description: '模拟登录原因至少需要5个字符',
        variant: 'destructive'
      })
      return
    }

    try {
      await startImpersonation(targetUserId, reason.trim())
      toast({
        title: '成功',
        description: `已成功切换到用户 "${targetUsername}" 的身份`,
        variant: 'success'
      })
      setOpen(false)
      setReason('')
    } catch (error) {
      console.error('模拟登录失败:', error)
      toast({
        title: '错误',
        description: '模拟登录失败，请重试',
        variant: 'destructive'
      })
    }
  }

  const handleCancel = () => {
    setOpen(false)
    setReason('')
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          disabled={loading}
        >
          <Users className="h-4 w-4 mr-2" />
          以此用户身份登录
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            模拟登录确认
          </DialogTitle>
          <DialogDescription>
            您即将以 <span className="font-medium text-foreground">"{targetUsername}"</span> 的身份登录系统。
            此操作将被记录在审计日志中。
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* 安全提示 */}
          <div className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">安全提示</p>
              <ul className="space-y-1 text-xs">
                <li>• 模拟登录将获得目标用户的所有权限</li>
                <li>• 所有操作都将被记录在审计日志中</li>
                <li>• 请确保操作的合法性和必要性</li>
              </ul>
            </div>
          </div>

          {/* 模拟原因 */}
          <div className="grid gap-2">
            <Label htmlFor="reason">模拟登录原因 *</Label>
            <Textarea
              id="reason"
              placeholder="请详细说明模拟登录的原因和目的..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="min-h-[80px]"
              maxLength={500}
            />
            <div className="text-xs text-muted-foreground text-right">
              {reason.length}/500
            </div>
          </div>


        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={loading || !reason.trim()}
            className="bg-orange-600 hover:bg-orange-700"
          >
            {loading ? '处理中...' : '确认模拟登录'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default ImpersonationButton

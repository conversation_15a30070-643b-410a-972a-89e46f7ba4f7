import React, { useEffect } from 'react'
import { Outlet, useSearchParams } from 'react-router-dom'
import { useMemoizedFn } from 'ahooks'
import { Header } from './Header'
import { Sidebar } from './Sidebar'
import { Breadcrumb } from './Breadcrumb'
import { ImpersonationBanner } from '../ImpersonationBanner'
import { useSidebar, useAppStore } from '../../stores'
import { useRouteChange } from '../../router/guards'
import { routes, generateBreadcrumbs } from '../../router/routes'
import { cn } from '../../utils'

/**
 * 主布局组件 - 修复面包屑更新循环问题
 */
export const MainLayout: React.FC = () => {
  const { collapsed } = useSidebar()
  const { setBreadcrumbs } = useAppStore()
  const location = useRouteChange()
  const [searchParams] = useSearchParams()

  // 使用ahooks的useMemoizedFn确保函数引用稳定
  const memoizedSetBreadcrumbs = useMemoizedFn(setBreadcrumbs)

  // 更新面包屑导航 - 直接使用store方法，避免通过useRouteGuard的循环
  useEffect(() => {
    const breadcrumbs = generateBreadcrumbs(routes, location.pathname, searchParams)
    memoizedSetBreadcrumbs(breadcrumbs)
  }, [location.pathname, searchParams, memoizedSetBreadcrumbs]) // 添加searchParams依赖

  return (
    <div className="h-screen flex bg-background">
      {/* 侧边栏 */}
      <Sidebar />
      
      {/* 主内容区域 */}
      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300",
        collapsed ? "ml-16" : "ml-64"
      )}>
        {/* 模拟登录状态Banner */}
        <ImpersonationBanner />

        {/* 顶部导航栏 */}
        <Header />

        {/* 面包屑导航 */}
        <div className="px-6 py-3 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <Breadcrumb />
        </div>
        
        {/* 页面内容 */}
        <main className="flex-1 overflow-auto">
          <div className="container mx-auto px-6 py-6">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  )
}

/**
 * 运营统计卡片组件
 * 
 * 显示总主播数、活跃主播数、总用户数等关键指标
 */

import React from 'react'
import { Card, CardContent } from '@/components/ui'
import { UsersRound, DollarSign, Activity } from 'lucide-react'
import { PermissionStatsCard } from './PermissionStatsCard'
import { cn } from '@/lib/utils'
import { formatNumber, formatCurrency } from '@/utils'
import { PERMISSIONS } from '@/constants/permissions'

export interface OperationsStats {
  zongyonghu: number
  zonglirun?: number
  zongchongzhi?: number
  totalConsumeThisMonth?: number
  todayRecharge?: number
  statsTime?: string
}

export interface StatsCardsProps {
  stats: OperationsStats | null
  loading?: boolean
  error?: string | null
  className?: string
}



// StatsCard组件已替换为PermissionStatsCard，提供权限控制功能

/**
 * 运营统计卡片组件
 */
export const StatsCards: React.FC<StatsCardsProps> = ({
  stats,
  loading = false,
  error,
  className
}) => {
  // 错误状态
  if (error) {
    return (
      <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
        <Card className="col-span-full">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">加载统计数据失败</p>
              <p className="text-xs text-muted-foreground mt-1">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
      {/* 总用户数 - 基础权限 */}
      <PermissionStatsCard
        title="总用户数"
        value={loading ? '' : formatNumber(stats?.zongyonghu || 0)}
        description="所有用户总数"
        icon={<UsersRound className="h-4 w-4" />}
        permissions={[PERMISSIONS.OPERATIONS.VIEW]}
        noPermissionBehavior="placeholder"
        noPermissionMessage="您没有查看用户统计的权限"
        loading={loading}
        className="border-blue-200 bg-blue-50/50"
      />
      {/* 利润金额 - 高敏感度权限 */}
      <PermissionStatsCard
        title="利润金额"
        value={loading ? '' : formatNumber(stats?.zonglirun || 0)}
        description="利润金额"
        icon={<DollarSign className="h-4 w-4" />}
        permissions={[PERMISSIONS.OPERATIONS.STATS.VIEW_PROFIT]}
        noPermissionBehavior="mask"
        noPermissionMessage="您没有查看利润数据的权限"
        loading={loading}
        className="border-pink-200 bg-pink-50/50"
      />
      {/* 时间区间充值金额 - 财务权限 */}
      {stats?.zongchongzhi !== undefined && (
        <PermissionStatsCard
          title="时间区间充值金额"
          value={loading ? '' : formatCurrency(stats.zongchongzhi)}
          description="选定时间范围内的充值金额"
          icon={<DollarSign className="h-4 w-4" />}
          permissions={[PERMISSIONS.OPERATIONS.STATS.VIEW_FINANCIAL]}
          noPermissionBehavior="mask"
          noPermissionMessage="您没有查看充值数据的权限"
          loading={loading}
          className="border-green-200 bg-green-50/50"
        />
      )}

      {/* 今日充值金额 */}
      {/*<StatsCard
        title="今日充值"
        value={loading ? '' : formatCurrency(stats?.todayRecharge || 0)}
        description="今天的充值总额"
        icon={<TrendingUp className="h-4 w-4" />}
        loading={loading}
        className="border-blue-200 bg-blue-50/50"
      />*/}

      {/* 本月消费金额 */}
      {/*{stats?.totalConsumeThisMonth !== undefined && (*/}
      {/*  <StatsCard*/}
      {/*    title="本月消费"*/}
      {/*    value={loading ? '' : `¥${formatAmount(stats.totalConsumeThisMonth)}`}*/}
      {/*    description="本月总消费金额"*/}
      {/*    icon={<Activity className="h-4 w-4" />}*/}
      {/*    loading={loading}*/}
      {/*    className="border-purple-200 bg-purple-50/50"*/}
      {/*  />*/}
      {/*)}*/}
    </div>
  )
}

export default StatsCards

import React, { useState, useEffect } from 'react'
import { ChevronDown, Search } from 'lucide-react'
import { Button } from './Button'
import { Input } from './Input'

import { UserService } from '@/services/user'
import type { User as UserType } from '@/types'

interface SimpleUserSelectorProps {
  value?: number
  onValueChange: (userId: number | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  error?: string
}

/**
 * 简化版用户选择器组件
 */
export const SimpleUserSelector: React.FC<SimpleUserSelectorProps> = ({
  value,
  onValueChange,
  placeholder = "选择用户",
  className = "",
  disabled = false,
  error
}) => {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [users, setUsers] = useState<UserType[]>([])
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null)

  // 加载用户列表
  const loadUsers = async (keyword?: string) => {
    try {
      setLoading(true)
      console.log('🔍 开始加载用户列表，关键词:', keyword)
      const result = await UserService.pageUsers({
        pageNum: 1,
        pageSize: 100,
        username: keyword || undefined,
        status: 1,
        identities: [2,3,4], // 只查询主播和代理用户
      })
      console.log('✅ 用户列表加载成功:', result)
      setUsers(result.records || [])
    } catch (error) {
      console.error('❌ 加载用户列表失败:', error)
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    if (open && users.length === 0) {
      loadUsers()
    }
  }, [open])

  // 搜索防抖
  useEffect(() => {
    if (!open) return
    
    const timer = setTimeout(() => {
      if (searchValue.trim()) {
        loadUsers(searchValue.trim())
      } else {
        loadUsers()
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchValue, open])

  // 根据value查找选中的用户
  useEffect(() => {
    if (value && users.length > 0) {
      const user = users.find(u => u.id === value)
      if (user) {
        setSelectedUser(user)
      } else if (value) {
        // 如果当前用户列表中没有，尝试单独获取
        UserService.getUserById(value).then(setSelectedUser).catch(() => {
          setSelectedUser(null)
        })
      }
    } else {
      setSelectedUser(null)
    }
  }, [value, users])

  const handleSelect = (user: UserType) => {
    console.log('👤 选择用户:', user)
    setSelectedUser(user)
    onValueChange(user.id)
    setOpen(false)
    setSearchValue('')
  }

  const handleClear = () => {
    setSelectedUser(null)
    onValueChange(undefined)
  }

  const getUserDisplayName = (user: UserType) => {
    return user.realName || user.nickname || user.username
  }

  const getUserSecondaryInfo = (user: UserType) => {
    const parts = []
    if (user.username) parts.push(`@${user.username}`)
    if (user.phone) parts.push(user.phone)
    return parts.join(' • ')
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="relative">
        <Button
          type="button"
          variant="outline"
          className={`w-full justify-between h-11 ${!selectedUser ? 'text-gray-500' : ''} ${error ? 'border-red-500' : ''}`}
          disabled={disabled}
          onClick={() => setOpen(!open)}
        >
          {selectedUser ? (
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                {getUserDisplayName(selectedUser).charAt(0).toUpperCase()}
              </div>
              <div className="flex flex-col items-start min-w-0">
                <span className="truncate font-medium">
                  {getUserDisplayName(selectedUser)}
                </span>
                {getUserSecondaryInfo(selectedUser) && (
                  <span className="text-xs text-gray-500 truncate">
                    {getUserSecondaryInfo(selectedUser)}
                  </span>
                )}
              </div>
            </div>
          ) : (
            <span>{placeholder}</span>
          )}
          <div className="flex items-center gap-1">
            {selectedUser && (
              <button
                type="button"
                className="h-6 w-6 p-0 hover:bg-gray-100 rounded"
                onClick={(e) => {
                  e.stopPropagation()
                  handleClear()
                }}
              >
                ×
              </button>
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>

        {open && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-hidden">
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索用户..."
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            
            <div className="max-h-48 overflow-y-auto">
              {loading ? (
                <div className="p-4 text-center text-sm text-gray-500">
                  加载中...
                </div>
              ) : users.length === 0 ? (
                <div className="p-4 text-center text-sm text-gray-500">
                  {searchValue ? '未找到匹配的用户' : '暂无用户数据'}
                </div>
              ) : (
                users.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center gap-2 p-2 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleSelect(user)}
                  >
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                      {getUserDisplayName(user).charAt(0).toUpperCase()}
                    </div>
                    <div className="flex flex-col flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">
                          {getUserDisplayName(user)}
                        </span>
                        {user.status === 1 && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">
                            启用
                          </span>
                        )}
                      </div>
                      {getUserSecondaryInfo(user) && (
                        <span className="text-xs text-gray-500 truncate">
                          {getUserSecondaryInfo(user)}
                        </span>
                      )}
                    </div>
                    {selectedUser?.id === user.id && (
                      <div className="text-blue-500">✓</div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {/* 点击外部关闭下拉框 */}
      {open && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setOpen(false)}
        />
      )}
    </div>
  )
}

export default SimpleUserSelector

import { Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select"
import { Label } from "./label"

interface TimePickerProps {
  value?: Date
  onChange?: (time: Date) => void
  className?: string
  disabled?: boolean
  placeholder?: string
}

export function TimePicker({ 
  value, 
  onChange, 
  className, 
  disabled = false,
}: TimePickerProps) {
  // 生成小时选项 (00-23)
  const hours = Array.from({ length: 24 }, (_, i) => 
    i.toString().padStart(2, '0')
  )
  
  // 生成分钟选项 (00-59)
  const minutes = Array.from({ length: 60 }, (_, i) => 
    i.toString().padStart(2, '0')
  )
  
  // 生成秒选项 (00-59)
  const seconds = Array.from({ length: 60 }, (_, i) => 
    i.toString().padStart(2, '0')
  )

  const currentHour = value ? value.getHours().toString().padStart(2, '0') : '00'
  const currentMinute = value ? value.getMinutes().toString().padStart(2, '0') : '00'
  const currentSecond = value ? value.getSeconds().toString().padStart(2, '0') : '00'

  const handleTimeChange = (type: 'hour' | 'minute' | 'second', newValue: string) => {
    if (!onChange) return
    
    const newTime = value ? new Date(value) : new Date()
    
    switch (type) {
      case 'hour':
        newTime.setHours(parseInt(newValue))
        break
      case 'minute':
        newTime.setMinutes(parseInt(newValue))
        break
      case 'second':
        newTime.setSeconds(parseInt(newValue))
        break
    }
    
    onChange(newTime)
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Clock className="h-4 w-4 text-muted-foreground" />
      
      {/* 小时选择 */}
      <div className="flex flex-col space-y-1">
        <Label className=" text-xs text-muted-foreground">时</Label>
        <Select
          value={currentHour}
          onValueChange={(value) => handleTimeChange('hour', value)}
          disabled={disabled}
        >
          <SelectTrigger className="w-18 h-8">
            <SelectValue placeholder="时" />
          </SelectTrigger>
          <SelectContent>
            {hours.map((hour) => (
              <SelectItem key={hour} value={hour}>
                {hour}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <span className="text-muted-foreground">:</span>

      {/* 分钟选择 */}
      <div className="flex flex-col space-y-1">
        <Label className="text-xs text-muted-foreground">分</Label>
        <Select
          value={currentMinute}
          onValueChange={(value) => handleTimeChange('minute', value)}
          disabled={disabled}
        >
          <SelectTrigger className="w-18 h-8">
            <SelectValue placeholder="分" />
          </SelectTrigger>
          <SelectContent>
            {minutes.map((minute) => (
              <SelectItem key={minute} value={minute}>
                {minute}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <span className="text-muted-foreground">:</span>

      {/* 秒选择 */}
      <div className="flex flex-col space-y-1">
        <Label className="text-xs text-muted-foreground">秒</Label>
        <Select
          value={currentSecond}
          onValueChange={(value) => handleTimeChange('second', value)}
          disabled={disabled}
        >
          <SelectTrigger className="w-18 h-8">
            <SelectValue placeholder="秒" />
          </SelectTrigger>
          <SelectContent>
            {seconds.map((second) => (
              <SelectItem key={second} value={second}>
                {second}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}

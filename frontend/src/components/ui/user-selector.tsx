import React, { useState, useEffect } from 'react'
import { Search, Check, ChevronDown } from 'lucide-react'
import { Button } from './Button'
import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './command'
import { Avatar, AvatarFallback, AvatarImage } from './avatar'
import { Badge } from './badge'
import { cn } from '@/utils'
import { UserService } from '@/services/user'
import type { User as UserType } from '@/types'

interface UserSelectorProps {
  value?: number
  onValueChange: (userId: number | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  error?: string
}

/**
 * 用户选择器组件
 * 支持搜索和选择用户
 */
export const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onValueChange,
  placeholder = "选择用户",
  className,
  disabled = false,
  error
}) => {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [users, setUsers] = useState<UserType[]>([])
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null)

  // 加载用户列表
  const loadUsers = async (keyword?: string) => {
    try {
      setLoading(true)
      console.log('🔍 开始加载用户列表，关键词:', keyword)
      const result = await UserService.pageUsers({
        pageNum: 1,
        pageSize: 50, // 限制返回数量
        username: keyword || undefined,
        status: 1 // 只显示启用的用户
      })
      console.log('✅ 用户列表加载成功:', result)
      setUsers(result.records || [])
    } catch (error) {
      console.error('❌ 加载用户列表失败:', error)
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    if (open) {
      loadUsers()
    }
  }, [open])

  // 搜索防抖
  useEffect(() => {
    if (!open) return

    const timer = setTimeout(() => {
      if (searchValue.trim()) {
        loadUsers(searchValue.trim())
      } else {
        loadUsers()
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchValue, open])

  // 根据value查找选中的用户
  useEffect(() => {
    if (value && users.length > 0) {
      const user = users.find(u => u.id === value)
      if (user) {
        setSelectedUser(user)
      } else if (value) {
        // 如果当前用户列表中没有，尝试单独获取
        UserService.getUserById(value).then(setSelectedUser).catch(() => {
          setSelectedUser(null)
        })
      }
    } else {
      setSelectedUser(null)
    }
  }, [value, users])

  // 直接使用后端返回的用户列表（后端已处理搜索）
  const filteredUsers = users

  const handleSelect = (user: UserType) => {
    console.log('👤 选择用户:', user)
    setSelectedUser(user)
    onValueChange(user.id)
    setOpen(false)
    setSearchValue('')
  }

  const handleClear = () => {
    setSelectedUser(null)
    onValueChange(undefined)
  }

  const getUserDisplayName = (user: UserType) => {
    return user.realName || user.nickname || user.username
  }

  const getUserSecondaryInfo = (user: UserType) => {
    const parts = []
    if (user.username) parts.push(`@${user.username}`)
    if (user.phone) parts.push(user.phone)
    return parts.join(' • ')
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between h-11",
              !selectedUser && "text-muted-foreground",
              error && "border-red-500"
            )}
            disabled={disabled}
          >
            {selectedUser ? (
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <Avatar className="w-6 h-6">
                  <AvatarImage src={selectedUser.avatar} />
                  <AvatarFallback className="text-xs">
                    {getUserDisplayName(selectedUser).charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col items-start min-w-0">
                  <span className="truncate font-medium">
                    {getUserDisplayName(selectedUser)}
                  </span>
                  {getUserSecondaryInfo(selectedUser) && (
                    <span className="text-xs text-muted-foreground truncate">
                      {getUserSecondaryInfo(selectedUser)}
                    </span>
                  )}
                </div>
              </div>
            ) : (
              <span>{placeholder}</span>
            )}
            <div className="flex items-center gap-1">
              {selectedUser && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-muted"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleClear()
                  }}
                >
                  ×
                </Button>
              )}
              <ChevronDown className="h-4 w-4 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput
                placeholder="搜索用户..."
                value={searchValue}
                onValueChange={setSearchValue}
                className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <CommandList>
              {loading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  加载中...
                </div>
              ) : filteredUsers.length === 0 ? (
                <CommandEmpty>
                  {searchValue ? '未找到匹配的用户' : '暂无用户数据'}
                </CommandEmpty>
              ) : (
                <CommandGroup>
                  {filteredUsers.map((user) => (
                    <CommandItem
                      key={user.id}
                      value={`${user.id}-${getUserDisplayName(user)}`}
                      onSelect={() => handleSelect(user)}
                      className="flex items-center gap-2 p-2 cursor-pointer hover:bg-accent"
                    >
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={user.avatar} />
                        <AvatarFallback className="text-xs">
                          {getUserDisplayName(user).charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium truncate">
                            {getUserDisplayName(user)}
                          </span>
                          {user.status === 1 && (
                            <Badge variant="secondary" className="text-xs">
                              启用
                            </Badge>
                          )}
                        </div>
                        {getUserSecondaryInfo(user) && (
                          <span className="text-xs text-muted-foreground truncate">
                            {getUserSecondaryInfo(user)}
                          </span>
                        )}
                      </div>
                      {selectedUser?.id === user.id && (
                        <Check className="h-4 w-4" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

export default UserSelector

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/Button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { CheckCircle, AlertCircle, Clock, XCircle, Users, UserCheck, AlertTriangle, Loader2, Play, RotateCcw } from "lucide-react";
import { toast } from "@/hooks/useToast";
import { UserSyncService, type UserSyncPreviewData, type VimUser, type UserSyncTaskStatus } from "@/services/userSync";

interface UserSyncWizardProps {
  onComplete?: () => void;
  onCancel?: () => void;
}

const UserSyncWizard: React.FC<UserSyncWizardProps> = ({ onComplete, onCancel }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [previewData, setPreviewData] = useState<UserSyncPreviewData | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<VimUser[]>([]);
  const [activeTab, setActiveTab] = useState<'valid' | 'invalid' | 'existing'>('valid');
  const [syncOptions] = useState({
    transactionMode: 'STRICT' as const,  // 默认严格模式，性能最佳
    conflictStrategy: 'SKIP' as const
  });
  const [taskId, setTaskId] = useState<string | null>(null);
  const [taskStatus, setTaskStatus] = useState<UserSyncTaskStatus | null>(null);
  const [loading, setLoading] = useState(false);

  const steps = [
    {
      title: '获取预览',
      description: '获取待同步用户列表',
      icon: Users
    },
    {
      title: '数据验证',
      description: '验证用户数据和检测冲突',
      icon: AlertTriangle
    },
    {
      title: '确认选择',
      description: '选择要同步的用户',
      icon: UserCheck
    },
    {
      title: '执行同步',
      description: '6步骤异步执行同步操作',
      icon: Clock
    },
    {
      title: '查看结果',
      description: '查看详细的同步结果',
      icon: CheckCircle
    }
  ];

  // 同步执行步骤（对应后端的6个步骤）
  const syncSteps = [
    {
      key: 'FETCH_AND_VALIDATE',
      title: '数据获取与验证',
      description: '获取用户数据并进行验证',
      weight: 20
    },
    {
      key: 'PREPROCESS_USERS',
      title: '用户数据预处理',
      description: '转换和预处理用户数据',
      weight: 10
    },
    {
      key: 'INSERT_USERS',
      title: '批量插入用户',
      description: '批量插入新用户到数据库',
      weight: 30
    },
    {
      key: 'ASSIGN_ROLES',
      title: '批量分配角色',
      description: '为用户分配相应角色',
      weight: 25
    },
    {
      key: 'PROCESS_AGENT_DEPTS',
      title: '处理代理部门',
      description: '为代理用户创建部门',
      weight: 10
    },
    {
      key: 'COMPLETE_CLEANUP',
      title: '完成清理',
      description: '清理缓存并生成报告',
      weight: 5
    }
  ];

  // 步骤指示器组件
  const StepIndicator: React.FC<{ steps: typeof steps; currentStep: number }> = ({ steps, currentStep }) => (
    <div className="w-full mb-8">
      {/* 桌面端步骤条 */}
      <div className="hidden md:flex items-center justify-between">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;

          return (
            <div key={index} className="flex items-center flex-1">
              <div className="flex items-center min-w-0">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 flex-shrink-0 ${
                  isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : isActive
                      ? 'bg-blue-500 border-blue-500 text-white'
                      : 'bg-gray-100 border-gray-300 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <Icon className="w-5 h-5" />
                  )}
                </div>
                <div className="ml-3 min-w-0">
                  <div className={`text-sm font-medium truncate ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </div>
                  <div className="text-xs text-gray-500 truncate">{step.description}</div>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className="flex-1 mx-4">
                  <Separator className="w-full" />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* 桌面端第4步详细子步骤 */}
      {currentStep === 3 && taskStatus && (
        <div className="hidden md:block mt-6 p-4 bg-blue-50 rounded-lg">
          <div className="text-sm font-medium text-blue-700 mb-3">同步执行详细步骤</div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {syncSteps.map((step, index) => {
              const isCurrentStep = step.key === taskStatus.currentStep;
              const isCompleted = syncSteps.findIndex(s => s.key === taskStatus.currentStep) > index;
              return (
                <div key={step.key} className={`flex items-center p-2 rounded ${
                  isCurrentStep ? 'bg-blue-100 border border-blue-300' :
                  isCompleted ? 'bg-green-100 border border-green-300' : 'bg-white border border-gray-200'
                }`}>
                  <div className={`w-3 h-3 rounded-full mr-2 flex-shrink-0 ${
                    isCompleted ? 'bg-green-500' :
                    isCurrentStep ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'
                  }`} />
                  <div className="min-w-0">
                    <div className={`text-xs font-medium truncate ${
                      isCurrentStep ? 'text-blue-700' :
                      isCompleted ? 'text-green-700' : 'text-gray-600'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500 truncate">{step.description}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 移动端步骤条 */}
      <div className="md:hidden">
        <div className="flex items-center justify-center mb-4">
          <div className="text-sm text-gray-500">
            步骤 {currentStep + 1} / {steps.length}
          </div>
        </div>
        <div className="flex items-center">
          <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
            steps[currentStep]
              ? 'bg-blue-500 border-blue-500 text-white'
              : 'bg-gray-100 border-gray-300 text-gray-400'
          }`}>
          </div>
          <div className="ml-3 flex-1">
            <div className="text-sm font-medium text-blue-600">
              {steps[currentStep].title}
            </div>
            <div className="text-xs text-gray-500">
              {steps[currentStep].description}
            </div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* 第4步时显示详细的同步子步骤 */}
        {currentStep === 3 && taskStatus && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="text-xs font-medium text-blue-700 mb-2">同步执行步骤</div>
            <div className="space-y-1">
              {syncSteps.map((step, index) => {
                const isCurrentStep = step.key === taskStatus.currentStep;
                const isCompleted = syncSteps.findIndex(s => s.key === taskStatus.currentStep) > index;
                return (
                  <div key={step.key} className="flex items-center text-xs">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      isCompleted ? 'bg-green-500' :
                      isCurrentStep ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'
                    }`} />
                    <span className={`${
                      isCurrentStep ? 'text-blue-700 font-medium' :
                      isCompleted ? 'text-green-700' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // 详细同步进度组件
  const SyncProgressPanel: React.FC<{ taskStatus: UserSyncTaskStatus }> = ({ taskStatus }) => {
    const getCurrentSyncStep = () => {
      return syncSteps.find(step => step.key === taskStatus.currentStep) || syncSteps[0];
    };

    const getStepStatus = (stepKey: string) => {
      const currentStepIndex = syncSteps.findIndex(step => step.key === taskStatus.currentStep);
      const stepIndex = syncSteps.findIndex(step => step.key === stepKey);

      if (stepIndex < currentStepIndex) return 'completed';
      if (stepIndex === currentStepIndex) return 'running';
      return 'pending';
    };

    const currentSyncStep = getCurrentSyncStep();

    return (
      <div className="space-y-6">
        {/* 总体进度 */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">总体进度</span>
            <span className="text-sm text-gray-500">{taskStatus.progressPercentage}%</span>
          </div>
          <Progress value={taskStatus.progressPercentage} className="h-3" />
          <div className="text-xs text-gray-500">{taskStatus.currentMessage}</div>
        </div>

        {/* 当前步骤详情 */}
        {taskStatus.stepDetails && (
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                {currentSyncStep.title}
              </CardTitle>
              <CardDescription>{currentSyncStep.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">
                    {taskStatus.stepDetails.totalUsers || 0}
                  </div>
                  <div className="text-gray-600">总用户数</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {taskStatus.stepDetails.processedUsers || 0}
                  </div>
                  <div className="text-gray-600">已处理</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {taskStatus.stepDetails.successUsers || 0}
                  </div>
                  <div className="text-gray-600">成功</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-red-600">
                    {taskStatus.stepDetails.failedUsers || 0}
                  </div>
                  <div className="text-gray-600">失败</div>
                </div>
              </div>

              {taskStatus.stepDetails.currentBatch > 0 && (
                <div className="mt-4 text-sm text-gray-600">
                  当前批次: {taskStatus.stepDetails.currentBatch} / {taskStatus.stepDetails.totalBatches}
                </div>
              )}

              {taskStatus.stepDetails.estimatedRemainingTime && (
                <div className="mt-2 text-sm text-gray-600">
                  预计剩余时间: {Math.ceil(taskStatus.stepDetails.estimatedRemainingTime / 1000)}秒
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 步骤列表 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">执行步骤</h4>
          <div className="space-y-2">
            {syncSteps.map((step, index) => {
              const status = getStepStatus(step.key);
              return (
                <div key={step.key} className="flex items-center space-x-3 p-3 rounded-lg border">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    status === 'completed'
                      ? 'bg-green-500 text-white'
                      : status === 'running'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-500'
                  }`}>
                    {status === 'completed' ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : status === 'running' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      status === 'running' ? 'text-blue-600' :
                      status === 'completed' ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500">{step.description}</div>
                  </div>
                  <div className="text-xs text-gray-400">{step.weight}%</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // 步骤1：获取预览
  const handleGetPreview = async () => {
    setLoading(true);
    try {
      const response = await UserSyncService.getSyncPreview();
      setPreviewData(response);
      setCurrentStep(1);
      toast({
        title: "成功",
        description: "获取预览数据成功",
      });
    } catch (error) {
      toast({
        title: "错误",
        description: "获取预览数据失败",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 步骤2：数据验证（自动执行）
  useEffect(() => {
    if (currentStep === 1 && previewData) {
      // 自动进入下一步
      setCurrentStep(2);
    }
  }, [currentStep, previewData]);

  // 步骤3：用户确认
  const handleConfirmSelection = () => {
    if (selectedUsers.length === 0) {
      toast({
        title: "警告",
        description: "请至少选择一个用户进行同步",
        variant: "destructive",
      });
      return;
    }
    setCurrentStep(3);
  };

  // 步骤4：执行同步
  const handleExecuteSync = async () => {
    if (!previewData) return;

    setLoading(true);
    try {
      const request = {
        previewId: previewData.previewId,
        selectedUserPhones: selectedUsers.map(user => user.phone),
        ...syncOptions,
        async: true
      };

      const response = await UserSyncService.executeSyncAsync(request);
      setTaskId(response);

      // 设置初始任务状态，立即显示进度界面
      setTaskStatus({
        taskId: response,
        status: 'RUNNING',
        currentStep: 'FETCH_AND_VALIDATE',
        totalSteps: 6,
        currentStepIndex: 0,
        progressPercentage: 0,
        startTime: Date.now(),
        endTime: 0,
        duration: 0,
        currentMessage: '正在初始化同步任务...',
        errorMessage: "",
        errorDetail: "",
        stepHistory: [],
        syncResult: null,
        stepDetails: {
          totalUsers: 0,
          processedUsers: 0,
          successUsers: 0,
          failedUsers: 0,
          skippedUsers: 0,
          currentBatch: 0,
          totalBatches: 0
        }
      });

      toast({
        title: "成功",
        description: "同步任务已创建，正在执行...",
      });

      // 立即跳转到步骤4显示进度
      setCurrentStep(4);

      // 开始轮询状态（不等待完成）
      pollTaskStatus(response);

    } catch (error) {
      console.error('创建同步任务失败:', error);

      // 根据错误类型显示不同的错误信息
      let errorMessage = "创建同步任务失败";
      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('超时')) {
          errorMessage = "请求超时，请检查网络连接或稍后重试";
        } else if (error.message.includes('网络')) {
          errorMessage = "网络连接失败，请检查网络设置";
        } else {
          errorMessage = error.message || "创建同步任务失败";
        }
      }

      toast({
        title: "错误",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 取消任务
  const handleCancelTask = async () => {
    if (!taskId) return;

    try {
      const success = await UserSyncService.cancelSyncTask(taskId);
      if (success) {
        toast({
          title: "任务已取消",
          description: "同步任务已被取消",
        });
        // 重新获取任务状态
        const updatedStatus = await UserSyncService.getSyncTaskStatus(taskId);
        setTaskStatus(updatedStatus);
      } else {
        toast({
          title: "取消失败",
          description: "任务可能已经完成或无法取消",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "取消失败",
        description: "取消任务失败",
        variant: "destructive",
      });
    }
  };

  // 轮询任务状态
  const pollTaskStatus = (id: string) => {
    let pollCount = 0;
    const maxPolls = 150; // 最大轮询次数（5分钟）

    const poll = async () => {
      try {
        const status = await UserSyncService.getSyncTaskStatus(id);
        setTaskStatus(status);
        pollCount++;

        if (status.status === 'COMPLETED' || status.status === 'FAILED' || status.status === 'CANCELLED') {
          // 任务完成，显示最终结果通知
          if (status.status === 'COMPLETED') {
            toast({
              title: "同步完成",
              description: "用户同步操作已完成",
            });
          } else if (status.status === 'FAILED') {
            toast({
              title: "同步失败",
              description: status.errorMessage || "同步过程中发生错误",
              variant: "destructive",
            });
          } else if (status.status === 'CANCELLED') {
            toast({
              title: "任务已取消",
              description: "同步任务已被取消",
            });
          }
          return; // 停止轮询
        }

        // 继续轮询
        if (pollCount < maxPolls) {
          // 根据任务状态调整轮询间隔
          const interval = status.status === 'RUNNING' ? 1500 : 3000; // 运行中1.5秒，其他3秒
          setTimeout(poll, interval);
        } else {
          toast({
            title: "轮询超时",
            description: "任务状态查询超时，请手动刷新",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error);
        // 轮询失败时不显示错误提示，避免干扰用户
        // 用户可以通过手动刷新按钮来获取最新状态
      }
    };

    // 立即开始第一次轮询
    poll();
  };

  // 处理用户选择
  const handleUserToggle = (user: VimUser, checked: boolean) => {
    if (checked) {
      setSelectedUsers([...selectedUsers, user]);
    } else {
      setSelectedUsers(selectedUsers.filter(u => u.phone !== user.phone));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked && previewData) {
      setSelectedUsers([...previewData.usersToSync]);
    } else {
      setSelectedUsers([]);
    }
  };

  // 获取身份类型显示文本
  const getIdentityText = (identity: number) => {
    switch (identity) {
      case 1: return '普通用户';
      case 2: return '线上主播';
      case 3: return '线下主播';
      case 4: return '代理';
      default: return '未知';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-6 h-6" />
            用户同步向导
          </CardTitle>
          <CardDescription>
            分步骤同步用户数据，提供详细的预览和控制选项
          </CardDescription>
        </CardHeader>
        <CardContent>
          <StepIndicator steps={steps} currentStep={currentStep} />
          
          <div className="mt-8">
            {/* 步骤0：开始同步 */}
            {currentStep === 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>开始同步</CardTitle>
                  <CardDescription>
                    点击开始获取待同步用户的预览数据
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={handleGetPreview} 
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        获取中...
                      </>
                    ) : (
                      "获取预览数据"
                    )}
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* 步骤1：同步预览 */}
            {currentStep === 1 && previewData && (
              <Card>
                <CardHeader>
                  <CardTitle>同步预览</CardTitle>
                  <CardDescription>
                    以下是待同步用户的统计信息
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{previewData.totalCount}</div>
                      <div className="text-sm text-gray-600">总用户数</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{previewData.newUserCount}</div>
                      <div className="text-sm text-gray-600">新用户</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">{previewData.existingUserCount}</div>
                      <div className="text-sm text-gray-600">已存在</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{previewData.invalidUserCount}</div>
                      <div className="text-sm text-gray-600">无效用户</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 步骤2：选择要同步的用户 */}
            {currentStep === 2 && previewData && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>选择要同步的用户</span>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedUsers.length === previewData.usersToSync.length && previewData.usersToSync.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                      <span className="text-sm">全选</span>
                    </div>
                  </CardTitle>
                  <CardDescription>
                    请选择要同步的用户，已选择 {selectedUsers.length} 个用户
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* 标签页导航 */}
                  <div className="mb-4">
                    <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                      <button
                        className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                          activeTab === 'valid'
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        onClick={() => setActiveTab('valid')}
                      >
                        可同步用户 ({previewData.usersToSync.length})
                      </button>
                      <button
                        className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                          activeTab === 'invalid'
                            ? 'bg-white text-red-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        onClick={() => setActiveTab('invalid')}
                      >
                        不匹配用户 ({previewData.invalidUsers?.length || 0})
                      </button>
                      <button
                        className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                          activeTab === 'existing'
                            ? 'bg-white text-yellow-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        onClick={() => setActiveTab('existing')}
                      >
                        已存在用户 ({Object.keys(previewData.existingUsers || {}).length})
                      </button>
                    </div>
                  </div>

                  {/* 可同步用户列表 */}
                  {activeTab === 'valid' && (
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {previewData.usersToSync.map((user, index) => (
                        <div key={index} className="flex items-center space-x-2 p-2 border rounded">
                          <Checkbox
                            checked={selectedUsers.some(u => u.phone === user.phone)}
                            onCheckedChange={(checked) => handleUserToggle(user, checked as boolean)}
                          />
                          <div className="flex-1">
                            <div className="font-medium">{user.username}</div>
                            <div className="text-sm text-gray-500">{user.phone}</div>
                          </div>
                          <Badge variant="outline">
                            {getIdentityText(user.identity)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 不匹配用户列表 */}
                  {activeTab === 'invalid' && (
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {previewData.invalidUsers && previewData.invalidUsers.length > 0 ? (
                        previewData.invalidUsers.map((error, index) => (
                          <div key={index} className="p-3 border border-red-200 rounded-lg bg-red-50">
                            <div className="flex items-start space-x-2">
                              <div className="flex-1">
                                <div className="font-medium text-red-800">{error.user.username}</div>
                                <div className="text-sm text-red-600">{error.user.phone}</div>
                                <div className="text-sm text-red-700 mt-1">{error.errorMessage}</div>
                                {error.fixSuggestion && (
                                  <div className="text-xs text-red-600 mt-1">
                                    建议：{error.fixSuggestion}
                                  </div>
                                )}
                              </div>
                              <Badge variant="destructive" className="text-xs">
                                {error.errorType}
                              </Badge>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          没有不匹配的用户
                        </div>
                      )}
                    </div>
                  )}

                  {/* 已存在用户列表 */}
                  {activeTab === 'existing' && (
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {Object.values(previewData.existingUsers || {}).length > 0 ? (
                        Object.values(previewData.existingUsers || {}).map((conflict: any, index) => (
                          <div key={index} className="p-3 border border-yellow-200 rounded-lg bg-yellow-50">
                            <div className="flex items-start space-x-2">
                              <div className="flex-1">
                                <div className="font-medium text-yellow-800">{conflict.syncUser.username}</div>
                                <div className="text-sm text-yellow-600">{conflict.syncUser.phone}</div>
                                <div className="text-sm text-yellow-700 mt-1">{conflict.conflictDescription}</div>
                                <div className="text-xs text-yellow-600 mt-1">
                                  建议：{conflict.suggestedAction}
                                </div>
                              </div>
                              <Badge variant="secondary" className="text-xs">
                                {conflict.conflictType}
                              </Badge>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          没有已存在的用户冲突
                        </div>
                      )}
                    </div>
                  )}

                  <div className="mt-4 flex gap-2">
                    <Button
                      onClick={handleConfirmSelection}
                      disabled={selectedUsers.length === 0}
                      className="flex-1"
                    >
                      确认选择 ({selectedUsers.length} 个用户)
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStep(0)}
                    >
                      重新开始
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 步骤3：执行同步 */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle>执行同步</CardTitle>
                  <CardDescription>
                    已选择 {selectedUsers.length} 个用户进行同步
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      即将同步 {selectedUsers.length} 个用户，请确认后点击开始同步。
                    </AlertDescription>
                  </Alert>
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleExecuteSync} 
                      disabled={loading}
                      className="flex-1"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          创建任务中...
                        </>
                      ) : (
                        "开始同步"
                      )}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setCurrentStep(2)}
                    >
                      返回
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 步骤4：同步进度和结果 */}
            {currentStep === 4 && taskStatus && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>同步进度</span>
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          taskStatus.status === 'COMPLETED' ? 'default' :
                          taskStatus.status === 'FAILED' ? 'destructive' :
                          taskStatus.status === 'RUNNING' ? 'secondary' : 'outline'
                        }>
                          {taskStatus.status === 'COMPLETED' ? '已完成' :
                           taskStatus.status === 'FAILED' ? '失败' :
                           taskStatus.status === 'RUNNING' ? '运行中' : '等待中'}
                        </Badge>
                        {taskStatus.status === 'RUNNING' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCancelTask()}
                            className="text-red-600 hover:text-red-700"
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            取消任务
                          </Button>
                        )}
                      </div>
                    </CardTitle>
                    <CardDescription>
                      任务ID: {taskId}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <SyncProgressPanel taskStatus={taskStatus} />

                    {/* 操作按钮 */}
                    <div className="mt-6 flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          if (taskId) {
                            const status = await UserSyncService.getSyncTaskStatus(taskId);
                            setTaskStatus(status);
                            toast({
                              title: "已刷新",
                              description: "任务状态已更新",
                            });
                          }
                        }}
                        className="flex items-center gap-2"
                      >
                        <RotateCcw className="w-4 h-4" />
                        手动刷新
                      </Button>

                      {taskStatus.status === 'FAILED' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // 重新开始同步流程
                            setCurrentStep(2);
                            setTaskId(null);
                            setTaskStatus(null);
                          }}
                          className="flex items-center gap-2"
                        >
                          <Play className="w-4 h-4" />
                          重新同步
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {taskStatus.status === 'COMPLETED' && taskStatus.syncResult && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">同步结果</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="text-center">
                              <div className="text-xl font-bold text-green-600">
                                {taskStatus.syncResult.successCount}
                              </div>
                              <div className="text-sm text-gray-600">成功</div>
                            </div>
                            <div className="text-center">
                              <div className="text-xl font-bold text-red-600">
                                {taskStatus.syncResult.failureCount}
                              </div>
                              <div className="text-sm text-gray-600">失败</div>
                            </div>
                            <div className="text-center">
                              <div className="text-xl font-bold text-yellow-600">
                                {taskStatus.syncResult.skipCount}
                              </div>
                              <div className="text-sm text-gray-600">跳过</div>
                            </div>
                            <div className="text-center">
                              <div className="text-xl font-bold text-blue-600">
                                {(() => {
                                  const total = taskStatus.syncResult?.totalCount || 0;
                                  const success = taskStatus.syncResult?.successCount || 0;
                                  if (total === 0) return '0.0%';
                                  return ((success / total) * 100).toFixed(1) + '%';
                                })()}
                              </div>
                              <div className="text-sm text-gray-600">成功率</div>
                            </div>
                          </div>
                          
                          <div className="mt-4 flex gap-2">
                            <Button 
                              onClick={() => {
                                onComplete?.();
                              }}
                              className="flex-1"
                            >
                              完成
                            </Button>
                            <Button 
                              variant="outline" 
                              onClick={() => {
                                setCurrentStep(0);
                                setPreviewData(null);
                                setSelectedUsers([]);
                                setTaskId(null);
                                setTaskStatus(null);
                              }}
                            >
                              重新同步
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                    
                {taskStatus.status === 'FAILED' && (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>
                      同步失败: {taskStatus.errorMessage}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </div>

          {/* 底部操作按钮 */}
          {currentStep < 4 && (
            <div className="mt-6 flex justify-between">
              <Button 
                variant="outline" 
                onClick={onCancel}
              >
                取消
              </Button>
              
              {currentStep > 0 && currentStep < 3 && (
                <Button 
                  variant="outline" 
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  上一步
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSyncWizard;

/**
 * API相关常量 - 性能优化版本
 */
export const API_CONFIG = {
  BASE_URL: '/api', // 使用相对路径，通过Vite代理转发到后端
  TIMEOUT: 8000, // 优化超时时间为8秒
  TOKEN_KEY: 'Authorization',
  TOKEN_PREFIX: 'Bearer ',
  // 请求重试配置
  RETRY_COUNT: 2,
  RETRY_DELAY: 1000,
  // 缓存配置
  CACHE_ENABLED: true,
  CACHE_TTL: 5 * 60 * 1000, // 5分钟缓存
} as const

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  TOKEN: 'jcloud_token',
  TOKEN_EXPIRES: 'jcloud_token_expires',
  REFRESH_TOKEN: 'jcloud_refresh_token',
  USER_INFO: 'jcloud_user_info',
  PERMISSIONS: 'jcloud_permissions',
  ROLES: 'jcloud_roles',
  MENUS: 'jcloud_menus',
  TENANT_ID: 'jcloud_tenant_id',
  THEME: 'jcloud_theme',
  LANGUAGE: 'jcloud_language',
} as const

/**
 * 路由路径常量
 */
export const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  SYSTEM: '/system',
  SYSTEM_USER: '/system/user',
  SYSTEM_ROLE: '/system/role',
  SYSTEM_PERMISSION: '/system/permission',
  SYSTEM_MENU: '/system/menu',
  FINANCIAL: '/financial',
  FINANCIAL_DASHBOARD: '/financial/stats',
  YUNYING: '/yunying',
  YUNYING_ANCHORS: '/yunying/anchors',
  ANCHOR_PROFILE: '/yunying/profile',
  PROFILE: '/profile',
  NOT_FOUND: '/404',
} as const

/**
 * 状态常量
 */
export const STATUS = {
  DISABLED: 0,
  ENABLED: 1,
} as const

/**
 * 性别常量
 */
export const GENDER = {
  UNKNOWN: 0,
  MALE: 1,
  FEMALE: 2,
} as const

/**
 * 菜单类型常量
 */
export const MENU_TYPE = {
  DIRECTORY: 'M',
  MENU: 'C',
  BUTTON: 'F',
} as const

/**
 * 权限类型常量
 */
export const PERMISSION_TYPE = {
  MENU: 1,
  BUTTON: 2,
  API: 3,
} as const

/**
 * 分页默认配置
 */
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_SIZE: 10,
  PAGE_SIZES: [10, 20, 50, 100],
} as const

/**
 * 表单验证规则
 */
export const VALIDATION_RULES = {
  REQUIRED: { required: true, message: '此字段为必填项' },
  USERNAME: {
    required: true,
    pattern: /^[a-zA-Z0-9_]{4,20}$/,
    message: '用户名必须是4-20位字母、数字或下划线',
  },
  PASSWORD: {
    required: true,
    min: 6,
    max: 20,
    message: '密码长度必须在6-20位之间',
  },
  EMAIL: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址',
  },
  PHONE: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的手机号码',
  },
} as const

/**
 * 主题配置
 */
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const

/**
 * 语言配置
 */
export const LANGUAGE = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US',
} as const

// 导出权限相关常量
export * from './permissions.ts'

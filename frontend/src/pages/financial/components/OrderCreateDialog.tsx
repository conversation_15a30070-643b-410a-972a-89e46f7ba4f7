import React, { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { Calendar, User, CreditCard, Percent } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import DateRangePicker from '@/components/ui/date-range-picker'
import SimpleUserSelector from '@/components/ui/simple-user-selector'
import { OrderService } from '@/services/financial'
import type { OrderCreateRequest } from '@/types/financial'
import { toast } from '@/hooks/useToast'

interface OrderCreateDialogProps {
  open: boolean
  onClose: () => void
  onSuccess: () => void
  /** 预填的目标用户ID */
  prefilledUserId?: number
  /** 预填的目标用户名称 */
  prefilledUserName?: string
  /** 预填的用户类型 */
  prefilledUserType?: 'agent' | 'anchor'
}

/**
 * 订单创建对话框
 */
export const OrderCreateDialog: React.FC<OrderCreateDialogProps> = ({
  open,
  onClose,
  onSuccess,
  prefilledUserId,
  prefilledUserName,
  prefilledUserType
}) => {
  const [searchParams] = useSearchParams()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<OrderCreateRequest>({
    startTime: 0,
    endTime: 0,
    feeRate: 10,
    feePerson: '',
    feeAccount: '',
    targetUserId: 0
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  /**
   * 处理预填信息
   */
  useEffect(() => {
    if (open) {
      // 从URL参数获取预填信息
      const targetUserId = searchParams.get('targetUserId') || prefilledUserId?.toString()
      const targetUserName = searchParams.get('targetUserName') || prefilledUserName

      if (targetUserId && targetUserName) {
        setFormData(prev => ({
          ...prev,
          targetUserId: parseInt(targetUserId)
        }))
      }
    }
  }, [open, searchParams, prefilledUserId, prefilledUserName, prefilledUserType])

  /**
   * 处理表单字段变化
   */
  const handleFieldChange = (field: keyof OrderCreateRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  /**
   * 处理时间范围变化
   */
  const handleDateRangeChange = (range: { from?: Date; to?: Date } | undefined) => {
    if (range?.from && range?.to) {
      setFormData(prev => ({
        ...prev,
        startTime: Math.floor(range.from!.getTime() / 1000),
        endTime: Math.floor(range.to!.getTime() / 1000)
      }))

      // 清除时间相关错误
      setErrors(prev => ({
        ...prev,
        startTime: '',
        endTime: ''
      }))
    }
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // 验证目标用户
    if (!formData.targetUserId || formData.targetUserId <= 0) {
      newErrors.targetUserId = '请选择目标用户'
    }

    // 验证时间范围
    if (!formData.startTime || !formData.endTime) {
      newErrors.dateRange = '请选择时间范围'
    } else if (formData.startTime >= formData.endTime) {
      newErrors.dateRange = '结束时间必须晚于开始时间'
    }

    // 验证劳务比例
    if (!formData.feeRate || formData.feeRate <= 0 || formData.feeRate > 100) {
      newErrors.feeRate = '劳务比例必须在0.01%到100%之间'
    }

    // 验证收款人
    if (!formData.feePerson.trim()) {
      newErrors.feePerson = '收款人姓名不能为空'
    }

    // 验证收款账户
    if (!formData.feeAccount.trim()) {
      newErrors.feeAccount = '收款账户信息不能为空'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  /**
   * 提交表单
   */
  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    // 使用服务层验证
    const validation = OrderService.validateCreateRequest(formData)
    if (!validation.valid) {
      setErrors({ general: validation.error || '请求参数无效' })
      return
    }

    try {
      setLoading(true)
      
      // 检查是否可以创建订单
      const canCreate = await OrderService.canCreateOrder(formData)
      if (canCreate) {
        setErrors({ general: '该时间段已存在结算订单，请勿重复创建' })
        return
      }
      
      // 创建订单
      const orders = await OrderService.createOrders(formData)

      toast({
        title: "订单创建成功",
        description: `共创建 ${orders.length} 个订单，已按推广金额计算佣金`,
        variant: "success"
      })
      onSuccess()
      handleReset()
    } catch (error: any) {
      console.error('创建订单失败:', error)
      setErrors({ 
        general: error.message || '创建订单失败，请稍后重试' 
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 重置表单
   */
  const handleReset = () => {
    setFormData({
      startTime: 0,
      endTime: 0,
      feeRate: 10,
      feePerson: '',
      feeAccount: '',
      targetUserId: 0
    })
    setErrors({})
  }

  /**
   * 关闭对话框
   */
  const handleClose = () => {
    if (!loading) {
      handleReset()
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            创建佣金结算订单
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 通用错误提示 */}
          {errors.general && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.general}</p>
            </div>
          )}

          {/* 目标用户选择 */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <User className="w-4 h-4" />
              目标用户
            </Label>
            <SimpleUserSelector
              value={formData.targetUserId || undefined}
              onValueChange={(userId) => handleFieldChange('targetUserId', userId || 0)}
              placeholder="选择要为哪个用户创建订单"
              error={errors.targetUserId}
            />
            <p className="text-xs text-gray-500">
              选择要为哪个用户创建佣金结算订单
            </p>
          </div>

          {/* 时间范围选择 */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              结算时间范围
            </Label>
            <DateRangePicker
              onDateChange={handleDateRangeChange}
              className="w-full"
            />
            {errors.dateRange && (
              <p className="text-sm text-red-600">{errors.dateRange}</p>
            )}
            <p className="text-xs text-gray-500">
              基于充值记录的创建时间筛选，最长不超过365天
            </p>
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                💡 <strong>推广金额计算规则：</strong>
              </p>
              <ul className="text-sm text-blue-600 mt-2 space-y-1">
                <li>• <strong>代理推广金额</strong> = 代理自充值 + 所有下级主播的推广金额</li>
                <li>• <strong>主播推广金额</strong> = 主播自充值 + 所有下级用户的充值</li>
                <li>• 系统将为所有选中用户创建订单，按推广金额计算佣金</li>
              </ul>
            </div>
          </div>

          {/* 劳务比例 */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Percent className="w-4 h-4" />
              劳务比例 (%)
            </Label>
            <Input
              type="number"
              min="0.01"
              max="100"
              step="0.01"
              value={formData.feeRate}
              onChange={(e) => handleFieldChange('feeRate', parseFloat(e.target.value) || 0)}
              placeholder="请输入劳务比例"
            />
            {errors.feeRate && (
              <p className="text-sm text-red-600">{errors.feeRate}</p>
            )}
            <p className="text-xs text-gray-500">
              默认值：10%，支持用户修改
            </p>
          </div>

          {/* 收款人姓名 */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <User className="w-4 h-4" />
              收款人姓名
            </Label>
            <Input
              value={formData.feePerson}
              onChange={(e) => handleFieldChange('feePerson', e.target.value)}
              placeholder="请输入收款人姓名"
              maxLength={50}
            />
            {errors.feePerson && (
              <p className="text-sm text-red-600">{errors.feePerson}</p>
            )}
          </div>

          {/* 收款账户信息 */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              收款账户信息
            </Label>
            <Input
              value={formData.feeAccount}
              onChange={(e) => handleFieldChange('feeAccount', e.target.value)}
              placeholder="请输入收款账户信息"
              maxLength={100}
            />
            {errors.feeAccount && (
              <p className="text-sm text-red-600">{errors.feeAccount}</p>
            )}
            <p className="text-xs text-gray-500">
              银行卡号、支付宝账号等收款信息
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? '创建中...' : '创建订单'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

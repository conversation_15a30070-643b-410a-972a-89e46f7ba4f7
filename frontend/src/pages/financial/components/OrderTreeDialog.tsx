import React, { useState, useEffect } from 'react'
import { TreePine, ChevronDown, ChevronRight, User, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { OrderService } from '@/services/financial'
import type { OrderVO } from '@/types/financial'

interface OrderTreeDialogProps {
  open: boolean
  onClose: () => void
  order: OrderVO | null
}

/**
 * 订单树形结构对话框
 */
export const OrderTreeDialog: React.FC<OrderTreeDialogProps> = ({
  open,
  onClose,
  order
}) => {
  const [loading, setLoading] = useState(false)
  const [mainOrderWithSubs, setMainOrderWithSubs] = useState<OrderVO | null>(null)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  /**
   * 加载主订单及其子订单
   */
  const loadMainOrderWithSubs = async () => {
    if (!order || !order.isMainOrder) {
      return
    }

    try {
      setLoading(true)
      const result = await OrderService.getMainOrderWithSubOrders(order.payid)
      setMainOrderWithSubs(result)
      
      // 默认展开主订单
      setExpandedItems(new Set([result.payid]))
    } catch (error) {
      console.error('加载订单树失败:', error)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 切换展开/折叠状态
   */
  const toggleExpanded = (payid: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(payid)) {
        newSet.delete(payid)
      } else {
        newSet.add(payid)
      }
      return newSet
    })
  }

  /**
   * 获取状态徽章样式
   */
  const getStatusBadgeVariant = (status: number) => {
    switch (status) {
      case 0: return 'secondary' // 已创建
      case 1: return 'default'   // 已计算
      case 2: return 'warning'   // 待结算
      case 3: return 'success'   // 已结算
      case 4: return 'destructive' // 已取消
      default: return 'secondary'
    }
  }

  /**
   * 渲染订单节点
   */
  const renderOrderNode = (orderItem: OrderVO, level: number = 0) => {
    const hasSubOrders = orderItem.subOrders && orderItem.subOrders.length > 0
    const isExpanded = expandedItems.has(orderItem.payid)
    const paddingLeft = level * 24

    return (
      <div key={orderItem.payid} className="border rounded-lg">
        <Collapsible
          open={isExpanded}
          onOpenChange={() => toggleExpanded(orderItem.payid)}
        >
          <CollapsibleTrigger asChild>
            <div
              className="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer"
              style={{ paddingLeft: paddingLeft + 16 }}
            >
              <div className="flex items-center gap-3 flex-1">
                {hasSubOrders && (
                  <div className="flex-shrink-0">
                    {isExpanded ? (
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-gray-500" />
                    )}
                  </div>
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-900 font-mono text-sm">
                      {orderItem.payid}
                    </span>
                    {orderItem.isMainOrder ? (
                      <Badge variant="default">主订单</Badge>
                    ) : (
                      <Badge variant="secondary">子订单</Badge>
                    )}
                    <Badge variant={getStatusBadgeVariant(orderItem.orderStatus)}>
                      {orderItem.orderStatusDesc}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    {orderItem.agent && (
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span>代理: {orderItem.agentNickname || orderItem.agent}</span>
                      </div>
                    )}
                    {orderItem.anchor && (
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span>主播: {orderItem.anchorNickname || orderItem.anchor}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
                    <DollarSign className="w-3 h-3" />
                    {OrderService.formatAmount(orderItem.totalAmount)}
                  </div>
                  <div className="text-xs text-green-600">
                    佣金: {OrderService.formatAmount(orderItem.feeActual)}
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleTrigger>
          
          {hasSubOrders && (
            <CollapsibleContent>
              <div className="border-t bg-gray-50/50">
                {orderItem.subOrders!.map(subOrder => (
                  <div key={subOrder.payid} className="border-b last:border-b-0">
                    {renderOrderNode(subOrder, level + 1)}
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          )}
        </Collapsible>
      </div>
    )
  }

  /**
   * 计算总计信息
   */
  const calculateTotals = () => {
    if (!mainOrderWithSubs) {
      return { totalAmount: 0, totalFee: 0, orderCount: 0 }
    }

    let totalAmount = mainOrderWithSubs.totalAmount
    let totalFee = mainOrderWithSubs.feeActual
    let orderCount = 1

    if (mainOrderWithSubs.subOrders) {
      mainOrderWithSubs.subOrders.forEach(subOrder => {
        totalAmount += subOrder.totalAmount
        totalFee += subOrder.feeActual
        orderCount += 1
      })
    }

    return { totalAmount, totalFee, orderCount }
  }

  // 当对话框打开且有订单时，加载数据
  useEffect(() => {
    if (open && order) {
      loadMainOrderWithSubs()
    }
  }, [open, order])

  if (!order) {
    return null
  }

  const totals = calculateTotals()

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TreePine className="w-5 h-5" />
            订单树形结构
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 统计信息 */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{totals.orderCount}</div>
              <div className="text-sm text-gray-600">订单总数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {OrderService.formatAmount(totals.totalAmount)}
              </div>
              <div className="text-sm text-gray-600">充值总额</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {OrderService.formatAmount(totals.totalFee)}
              </div>
              <div className="text-sm text-gray-600">佣金总额</div>
            </div>
          </div>

          {/* 订单树 */}
          <div className="space-y-2">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-gray-500">加载中...</div>
              </div>
            ) : mainOrderWithSubs ? (
              renderOrderNode(mainOrderWithSubs)
            ) : (
              <div className="text-center py-8 text-gray-500">
                暂无数据
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-between pt-4 border-t">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setExpandedItems(new Set([mainOrderWithSubs?.payid || '']))}
                disabled={loading}
              >
                全部折叠
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  const allIds = new Set<string>()
                  if (mainOrderWithSubs) {
                    allIds.add(mainOrderWithSubs.payid)
                    mainOrderWithSubs.subOrders?.forEach(sub => allIds.add(sub.payid))
                  }
                  setExpandedItems(allIds)
                }}
                disabled={loading}
              >
                全部展开
              </Button>
            </div>
            
            <Button onClick={onClose}>
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

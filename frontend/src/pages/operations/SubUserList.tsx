/**
 * 下级用户列表页面
 * 
 * 展示主播的下级用户列表，支持搜索、筛选、排序和分页功能
 */

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import {
  Search,
  Filter,
  Download,
  RefreshCw,
  User,
  Phone,
  Calendar,
  DollarSign,
  TrendingUp,
  ArrowUpDown,
  CreditCard,
  ShoppingCart,
  ArrowLeft
} from 'lucide-react'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  Skeleton,
  Alert,
  AlertDescription
} from '@/components/ui'
import { OperationsService } from '@/services/operations'
import type { SubUserQueryRequest, SubUserResponse, PageResult } from '@/services/operations'
import { formatTimestamp, formatCurrency } from '@/utils'
import { toast } from '@/hooks'

/**
 * 用户状态映射
 */
const USER_STATE_MAP = {
  1: { label: '正常', variant: 'default' as const },
  2: { label: '禁用', variant: 'destructive' as const }
}

/**
 * 认证状态映射
 */
const AUTH_STATUS_MAP = {
  0: { label: '未实名', variant: 'secondary' as const },
  1: { label: '已实名', variant: 'default' as const }
}

/**
 * 下级用户列表页面组件
 */
export default function SubUserList() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const anchorId = Number(searchParams.get('anchorId'))
  const anchorName = searchParams.get('anchorName') || '未知主播'

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<PageResult<SubUserResponse> | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 查询参数状态
  const [queryParams, setQueryParams] = useState<SubUserQueryRequest>({
    pageNum: 1,
    pageSize: 20,
    orderBy: 'createTime',
    orderDirection: 'DESC'
  })

  // 搜索表单状态
  const [searchForm, setSearchForm] = useState({
    nickname: '',
    username: '',
    phone: '',
    state: 'all',
    isAuth: 'all',
    hasFirstRecharge: 'all'
  })

  /**
   * 获取下级用户列表
   */
  const fetchSubUsers = useCallback(async (params: SubUserQueryRequest) => {
    if (!anchorId) {
      setError('缺少主播ID参数')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await OperationsService.getSubUsers(anchorId, params)
      setData(result)
    } catch (err) {
      let errorMessage = '获取下级用户列表失败'
      let userFriendlyMessage = '请稍后重试，如问题持续存在请联系管理员'

      if (err instanceof Error) {
        errorMessage = err.message
        // 根据错误类型提供用户友好的提示
        if (err.message.includes('网络')) {
          userFriendlyMessage = '网络连接异常，请检查网络后重试'
        } else if (err.message.includes('权限')) {
          userFriendlyMessage = '您没有权限查看此数据，请联系管理员'
        } else if (err.message.includes('参数')) {
          userFriendlyMessage = '查询参数有误，请检查输入条件'
        } else if (err.message.includes('超时')) {
          userFriendlyMessage = '查询超时，请稍后重试或缩小查询范围'
        }
      }

      setError(errorMessage)
      toast({
        title: '获取数据失败',
        description: userFriendlyMessage,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [anchorId])

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    const newParams: SubUserQueryRequest = {
      ...queryParams,
      pageNum: 1,
      ...Object.fromEntries(
        Object.entries(searchForm).filter(([_, value]) => value !== '' && value !== 'all')
      )
    }
    setQueryParams(newParams)
    fetchSubUsers(newParams)
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    const resetForm = {
      nickname: '',
      username: '',
      phone: '',
      state: 'all',
      isAuth: 'all',
      hasFirstRecharge: 'all'
    }
    setSearchForm(resetForm)

    const resetParams: SubUserQueryRequest = {
      pageNum: 1,
      pageSize: 20,
      orderBy: 'createTime',
      orderDirection: 'DESC'
    }
    setQueryParams(resetParams)
    fetchSubUsers(resetParams)
  }

  /**
   * 处理分页变化
   */
  const handlePageChange = (page: number) => {
    const newParams = { ...queryParams, pageNum: page }
    setQueryParams(newParams)
    fetchSubUsers(newParams)
  }

  /**
   * 处理每页条数变化
   */
  const handlePageSizeChange = (pageSize: number) => {
    const newParams = { ...queryParams, pageNum: 1, pageSize }
    setQueryParams(newParams)
    fetchSubUsers(newParams)
  }

  /**
   * 处理排序变化
   */
  const handleSortChange = (field: string) => {
    const newDirection = queryParams.orderBy === field && queryParams.orderDirection === 'DESC' 
      ? 'ASC' 
      : 'DESC'
    
    const newParams = {
      ...queryParams,
      orderBy: field,
      orderDirection: newDirection as 'ASC' | 'DESC'
    }
    setQueryParams(newParams)
    fetchSubUsers(newParams)
  }

  /**
   * 处理导出
   */
  const handleExport = () => {
    toast({
      title: '导出功能',
      description: '导出功能正在开发中...',
      variant: 'default'
    })
  }

  /**
   * 处理查看充值明细
   */
  const handleViewRechargeDetails = (user: SubUserResponse) => {
    navigate(`/yunying/recharge-details?userId=${user.id}&userName=${encodeURIComponent(user.nickname)}`)
  }

  /**
   * 处理查看消费明细
   */
  const handleViewConsumeDetails = (user: SubUserResponse) => {
    navigate(`/yunying/consume-details?userId=${user.id}&userName=${encodeURIComponent(user.nickname)}`)
  }

  /**
   * 返回主播详情页面
   */
  const handleBackToAnchorProfile = () => {
    navigate('/yunying/profile')
  }

  // 初始化加载数据
  useEffect(() => {
    fetchSubUsers(queryParams)
  }, [])

  // 渲染加载状态
  if (loading && !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>下级用户列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={handleBackToAnchorProfile}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回主播详情
          </Button>
          <div>
            <h1 className="text-2xl font-bold">下级用户列表</h1>
            <p className="text-muted-foreground">主播：{anchorName}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchSubUsers(queryParams)}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 搜索筛选区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            搜索筛选
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">昵称</label>
              <Input
                placeholder="请输入昵称"
                value={searchForm.nickname}
                onChange={(e) => setSearchForm(prev => ({ ...prev, nickname: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">用户名</label>
              <Input
                placeholder="请输入用户名"
                value={searchForm.username}
                onChange={(e) => setSearchForm(prev => ({ ...prev, username: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">手机号</label>
              <Input
                placeholder="请输入手机号"
                value={searchForm.phone}
                onChange={(e) => setSearchForm(prev => ({ ...prev, phone: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">状态</label>
              <Select
                value={searchForm.state}
                onValueChange={(value) => setSearchForm(prev => ({ ...prev, state: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="1">正常</SelectItem>
                  <SelectItem value="2">禁用</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">实名状态</label>
              <Select
                value={searchForm.isAuth}
                onValueChange={(value) => setSearchForm(prev => ({ ...prev, isAuth: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择实名状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="0">未实名</SelectItem>
                  <SelectItem value="1">已实名</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">首充状态</label>
              <Select
                value={searchForm.hasFirstRecharge}
                onValueChange={(value) => setSearchForm(prev => ({ ...prev, hasFirstRecharge: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择首充状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="true">已首充</SelectItem>
                  <SelectItem value="false">未首充</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <Button onClick={handleSearch} disabled={loading}>
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
            <Button variant="outline" onClick={handleReset}>
              重置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>用户列表</CardTitle>
            <div className="text-sm text-muted-foreground">
              共 {data?.total || 0} 条记录
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>用户ID</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('nickname')}
                  >
                    <div className="flex items-center">
                      昵称
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead>用户名</TableHead>
                  <TableHead>手机号</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>实名状态</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('totalRecharge')}
                  >
                    <div className="flex items-center">
                      总充值
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('totalConsume')}
                  >
                    <div className="flex items-center">
                      总消费
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('createTime')}
                  >
                    <div className="flex items-center">
                      注册时间
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead>首充时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : data?.records?.length ? (
                  data.records.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-muted-foreground" />
                          {user.nickname}
                        </div>
                      </TableCell>
                      <TableCell>{user.username}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                          {user.phone}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={USER_STATE_MAP[user.state as keyof typeof USER_STATE_MAP]?.variant}>
                          {USER_STATE_MAP[user.state as keyof typeof USER_STATE_MAP]?.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={AUTH_STATUS_MAP[user.isauth as keyof typeof AUTH_STATUS_MAP]?.variant}>
                          {AUTH_STATUS_MAP[user.isauth as keyof typeof AUTH_STATUS_MAP]?.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                          {formatCurrency(user.totalRecharge)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <TrendingUp className="h-4 w-4 mr-1 text-blue-600" />
                          {formatCurrency(user.totalConsume)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                          {formatTimestamp(user.createTime)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.firstRechargeTime ? (
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            {formatTimestamp(user.firstRechargeTime)}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">未首充</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewRechargeDetails(user)}
                            title="查看充值明细"
                          >
                            <CreditCard className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewConsumeDetails(user)}
                            title="查看消费明细"
                          >
                            <ShoppingCart className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={11} className="text-center py-8 text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* 分页组件 */}
          {data && data.total > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground w-14">每页显示</span>
                <Select
                  value={queryParams.pageSize.toString()}
                  onValueChange={(value) => handlePageSizeChange(Number(value))}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">条</span>
              </div>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                        onClick={() => handlePageChange(queryParams.pageNum - 1)}
                        className={queryParams.pageNum <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                        size={undefined}                    />
                  </PaginationItem>
                  
                  {Array.from({ length: Math.min(5, data.totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <PaginationItem key={page}>
                        <PaginationLink
                            onClick={() => handlePageChange(page)}
                            isActive={page === queryParams.pageNum}
                            className="cursor-pointer" size={undefined}                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}
                  
                  <PaginationItem>
                    <PaginationNext
                        onClick={() => handlePageChange(queryParams.pageNum + 1)}
                        className={queryParams.pageNum >= data.totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                        size={undefined}                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 部门树形表格组件
 *
 * 显示部门树形结构数据，支持展开/收起、排序、操作等功能
 * 统一重构版本 - 完整的权限控制实现
 */

import React, { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Button,
  Checkbox,
  Badge
} from '../../../../components/ui'
import { DataPagination } from '../../../../components/common/DataPagination'
import { ActionPermissionButton } from '../../../../components/auth/PermissionWrapper'
import {
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  ChevronRight,
  ChevronDown,
  Plus,
  Users,
  UserCheck,
  UserX,
  Building2
} from 'lucide-react'
import type { Dept, DeptTreeNode } from '../../../../types/dept'
import { getDeptStatusLabel, DeptStatus, buildDeptTree } from '../../../../types/dept'

// 简单的日期格式化函数
const formatDateTime = (dateValue: string | number) => {
  if (!dateValue) return '-'

  // 处理时间戳（秒）和字符串格式
  let date: Date
  if (typeof dateValue === 'number') {
    // 时间戳格式（秒），需要转换为毫秒
    date = new Date(dateValue * 1000)
  } else {
    // 字符串格式
    date = new Date(dateValue)
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '-'
  }

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export interface DeptTreeTableProps {
  data: Dept[]
  loading: boolean
  selectedIds: number[]
  onSelectionChange: (ids: number[]) => void
  onEdit: (dept: Dept) => void
  onDelete: (dept: Dept) => void
  onAddChild: (parentDept: Dept) => void
  onToggleStatus: (dept: Dept) => void
  onManageUsers: (dept: Dept) => void
  pagination: {
    current: number
    pageSize: number
    total: number
    onChange: (page: number, pageSize: number) => void
  }
  sortConfig?: {
    field: string
    order: 'asc' | 'desc'
  }
  onSort?: (field: string) => void
}

/**
 * 部门树形表格组件
 */
const DeptTreeTable: React.FC<DeptTreeTableProps> = ({
  data,
  loading,
  selectedIds,
  onSelectionChange,
  onEdit,
  onDelete,
  onAddChild,
  onToggleStatus,
  onManageUsers,
  pagination,
  sortConfig,
  onSort
}) => {
  const [treeData, setTreeData] = useState<DeptTreeNode[]>([])
  const [expandedIds, setExpandedIds] = useState<Set<number>>(new Set())
  const [flatData, setFlatData] = useState<DeptTreeNode[]>([])

  // 构建树形数据
  useEffect(() => {
    const tree = buildDeptTree(data)
    setTreeData(tree)
    
    // 默认展开第一层
    const firstLevelIds = tree.map(node => node.id)
    setExpandedIds(new Set(firstLevelIds))
  }, [data])

  // 扁平化显示数据
  useEffect(() => {
    const flattenWithExpanded = (nodes: DeptTreeNode[], level = 0): DeptTreeNode[] => {
      const result: DeptTreeNode[] = []
      
      nodes.forEach(node => {
        const nodeWithLevel = { ...node, level, expanded: expandedIds.has(node.id) }
        result.push(nodeWithLevel)
        
        if (expandedIds.has(node.id) && node.children.length > 0) {
          result.push(...flattenWithExpanded(node.children, level + 1))
        }
      })
      
      return result
    }
    
    setFlatData(flattenWithExpanded(treeData))
  }, [treeData, expandedIds])

  // 处理展开/收起
  const handleToggleExpand = (id: number) => {
    const newExpandedIds = new Set(expandedIds)
    if (newExpandedIds.has(id)) {
      newExpandedIds.delete(id)
    } else {
      newExpandedIds.add(id)
    }
    setExpandedIds(newExpandedIds)
  }

  // 处理全选
  const isAllSelected = data.length > 0 && selectedIds.length === data.length
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < data.length

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(data.map(item => item.id))
    } else {
      onSelectionChange([])
    }
  }

  // 处理单选
  const handleSelectItem = (id: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, id])
    } else {
      onSelectionChange(selectedIds.filter(selectedId => selectedId !== id))
    }
  }

  // 处理排序
  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field)
    }
  }

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (!sortConfig || sortConfig.field !== field) {
      return <ArrowUpDown className="w-4 h-4 ml-1 opacity-50" />
    }
    
    return sortConfig.order === 'asc' 
      ? <ArrowUp className="w-4 h-4 ml-1" />
      : <ArrowDown className="w-4 h-4 ml-1" />
  }

  // 处理页面大小变化
  const handlePageSizeChange = (newPageSize: number) => {
    pagination.onChange(1, newPageSize) // 切换页面大小时回到第一页
  }

  // 渲染部门名称（带层级缩进和展开图标）
  const renderDeptName = (dept: DeptTreeNode) => {
    const hasChildren = dept.children && dept.children.length > 0
    const isExpanded = expandedIds.has(dept.id)
    
    return (
      <div className="flex items-center" style={{ paddingLeft: `${dept.level * 20}px` }}>
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="w-6 h-6 p-0 mr-2"
            onClick={() => handleToggleExpand(dept.id)}
          >
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </Button>
        ) : (
          <div className="w-6 h-6 mr-2" />
        )}
        <span className="font-medium">{dept.deptName}</span>
        {hasChildren && (
          <Badge variant="outline" className="ml-2 text-xs">
            {dept.children.length}
          </Badge>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-md border overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">选择</TableHead>
                <TableHead className="w-64">部门名称</TableHead>
                <TableHead className="w-32">部门编码</TableHead>
                <TableHead className="w-32">负责人</TableHead>
                <TableHead className="w-32">联系电话</TableHead>
                <TableHead className="w-40">联系邮箱</TableHead>
                <TableHead className="w-20">状态</TableHead>
                <TableHead className="w-20">排序</TableHead>
                <TableHead className="w-40">创建时间</TableHead>
                <TableHead className="w-48">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
            <Building2 className="w-8 h-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <div className="text-lg font-medium text-muted-foreground">暂无部门数据</div>
            <div className="text-sm text-muted-foreground">
              没有找到符合条件的部门，请尝试调整搜索条件
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 表格 - 添加水平滚动 */}
      <div className="rounded-md border overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="选择所有部门"
                  data-state={isIndeterminate ? "indeterminate" : isAllSelected ? "checked" : "unchecked"}
                />
              </TableHead>
              <TableHead className="w-64">部门名称</TableHead>
              <TableHead 
                className="w-32 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('deptCode')}
              >
                <div className="flex items-center">
                  部门编码
                  {renderSortIcon('deptCode')}
                </div>
              </TableHead>
              <TableHead className="w-32">负责人</TableHead>
              <TableHead className="w-32">联系电话</TableHead>
              <TableHead className="w-40">联系邮箱</TableHead>
              <TableHead 
                className="w-20 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  状态
                  {renderSortIcon('status')}
                </div>
              </TableHead>
              <TableHead 
                className="w-20 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('sortOrder')}
              >
                <div className="flex items-center">
                  排序
                  {renderSortIcon('sortOrder')}
                </div>
              </TableHead>
              <TableHead 
                className="w-40 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('createTime')}
              >
                <div className="flex items-center">
                  创建时间
                  {renderSortIcon('createTime')}
                </div>
              </TableHead>
              <TableHead className="w-48">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {flatData.map((dept) => (
              <TableRow key={dept.id} className="hover:bg-muted/50">
                <TableCell>
                  <Checkbox
                    checked={selectedIds.includes(dept.id)}
                    onCheckedChange={(checked) => handleSelectItem(dept.id, checked as boolean)}
                    aria-label={`选择部门 ${dept.deptName}`}
                  />
                </TableCell>
                <TableCell>
                  {renderDeptName(dept)}
                </TableCell>
                <TableCell className="font-mono text-sm">{dept.deptCode}</TableCell>
                <TableCell>{dept.leaderName || '-'}</TableCell>
                <TableCell>{dept.phone || '-'}</TableCell>
                <TableCell>{dept.email || '-'}</TableCell>
                <TableCell>
                  <Badge variant={dept.status === DeptStatus.ENABLED ? 'default' : 'secondary'}>
                    {getDeptStatusLabel(dept.status)}
                  </Badge>
                </TableCell>
                <TableCell>{dept.sortOrder}</TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {formatDateTime(dept.createTime)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    {/* 编辑按钮 - 蓝色 */}
                    <ActionPermissionButton
                      module="dept"
                      action="edit"
                      config={{
                        text: '',
                        icon: Edit,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onEdit(dept),
                        title: '编辑部门',
                        className: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                      }}
                    />

                    {/* 添加子部门按钮 - 绿色 */}
                    <ActionPermissionButton
                      module="dept"
                      action="add"
                      config={{
                        text: '',
                        icon: Plus,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onAddChild(dept),
                        title: '添加子部门',
                        className: 'text-green-600 hover:text-green-700 hover:bg-green-50'
                      }}
                    />

                    {/* 管理用户按钮 - 橙色 */}
                    <ActionPermissionButton
                      module="dept"
                      action="edit"
                      config={{
                        text: '',
                        icon: Users,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onManageUsers(dept),
                        title: '管理用户',
                        className: 'text-orange-600 hover:text-orange-700 hover:bg-orange-50'
                      }}
                    />

                    {/* 状态切换按钮 - 动态颜色 */}
                    <ActionPermissionButton
                      module="dept"
                      action="edit"
                      config={{
                        text: '',
                        icon: dept.status === DeptStatus.ENABLED ? UserX : UserCheck,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onToggleStatus(dept),
                        title: dept.status === DeptStatus.ENABLED ? '禁用部门' : '启用部门',
                        className: dept.status === DeptStatus.ENABLED
                          ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                          : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                      }}
                    />

                    {/* 删除按钮 - 红色 */}
                    <ActionPermissionButton
                      module="dept"
                      action="delete"
                      config={{
                        text: '',
                        icon: Trash2,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onDelete(dept),
                        title: '删除部门',
                        className: 'text-red-600 hover:text-red-700 hover:bg-red-50'
                      }}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {/* 统一分页组件 */}
      <DataPagination
        current={pagination.current}
        pageSize={pagination.pageSize}
        total={pagination.total}
        onPageChange={(page) => {
          pagination.onChange(page, pagination.pageSize || 10)
        }}
        onPageSizeChange={handlePageSizeChange}
        showSizeChanger={true}
        showTotal={true}
        pageSizeOptions={[10, 20, 50, 100]}
      />
    </div>
  )
}

export default DeptTreeTable

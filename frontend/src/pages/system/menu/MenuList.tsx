import React, { useEffect, useState } from 'react'
import { Plus, Search, RefreshCw } from 'lucide-react'
import { Button, Input, Card, CardContent, CardHeader, CardTitle } from '../../../components/ui'
import { usePermission } from '../../../stores'
import { useMenuStore } from './hooks'
import { MenuTree, MenuForm, DeleteConfirmDialog } from './components'
import { MenuAction, type MenuTreeNode } from './types'
import { PERMISSIONS } from '../../../constants'



/**
 * 菜单管理页面
 */
const MenuList: React.FC = () => {
  const { hasPermission } = usePermission()

  // 菜单状态
  const {
    loading,
    action,
    visible,
    currentMenu,
    parentId,
    fetchMenuTree,
    searchMenus,
    openForm,
    closeForm,
    deleteMenu,
  } = useMenuStore()

  // 搜索关键词状态
  const [searchValue, setSearchValue] = useState('')

  // 删除确认对话框状态
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    menu: null as MenuTreeNode | null,
    loading: false
  })



  /**
   * 初始化数据
   */
  useEffect(() => {
    fetchMenuTree()
  }, [fetchMenuTree])

  /**
   * 处理搜索
   */
  const handleSearch = (value: string) => {
    setSearchValue(value)
    searchMenus(value)
  }

  /**
   * 处理刷新
   */
  const handleRefresh = async () => {
    try {
      await fetchMenuTree()
    } catch (error) {
      console.error('刷新菜单数据失败:', error)
    }
  }

  /**
   * 处理新增菜单
   */
  const handleAdd = (parentId?: number) => {
    openForm(MenuAction.ADD, undefined, parentId)
  }

  /**
   * 处理编辑菜单
   */
  const handleEdit = (menu: MenuTreeNode) => {
    openForm(MenuAction.EDIT, menu)
  }

  /**
   * 处理删除菜单 - 显示确认对话框
   */
  const handleDelete = (menu: MenuTreeNode) => {
    setDeleteDialog({
      open: true,
      menu,
      loading: false
    })
  }

  /**
   * 确认删除菜单
   */
  const handleDeleteConfirm = async () => {
    if (!deleteDialog.menu) return

    try {
      setDeleteDialog(prev => ({ ...prev, loading: true }))

      const success = await deleteMenu(deleteDialog.menu.id)

      if (success) {
        // 关闭对话框
        setDeleteDialog({
          open: false,
          menu: null,
          loading: false
        })
      }
    } catch (error) {
      console.error('删除菜单失败:', error)
      // axios拦截器已经统一处理了错误提示，这里不需要再显示Toast
    } finally {
      setDeleteDialog(prev => ({ ...prev, loading: false }))
    }
  }

  /**
   * 取消删除菜单
   */
  const handleDeleteCancel = () => {
    setDeleteDialog({
      open: false,
      menu: null,
      loading: false
    })
  }

  /**
   * 处理表单提交成功
   */
  const handleFormSuccess = (_action: MenuAction, _menuName?: string) => {
    // 自动关闭表单
    closeForm()

    // 刷新菜单树数据
    fetchMenuTree()

    // axios拦截器已经统一处理了成功提示，这里不需要再显示Toast
  }

  /**
   * 处理表单提交失败
   */
  const handleFormError = (_action: MenuAction, error: string) => {
    console.error('菜单操作失败:', error)
  }


  return (
    <div className="space-y-6">
      {/* 页面标题和操作栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">菜单管理</h1>
          <p className="text-muted-foreground">
            管理系统菜单结构，配置菜单权限和显示顺序
          </p>
        </div>

        <div className="flex items-center gap-2">
          {/* 刷新按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>

          {/* 新增按钮 */}
          {hasPermission(PERMISSIONS.MENU.ADD) && (
            <Button
              onClick={() => handleAdd()}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              新增菜单
            </Button>
          )}
        </div>
      </div>

      {/* 搜索栏 */}
      <Card>
        <CardHeader>
          <CardTitle>搜索筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索菜单名称..."
                  value={searchValue}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 菜单树 */}
      <Card>
        <CardHeader>
          <CardTitle>菜单列表</CardTitle>
        </CardHeader>
        <CardContent>
          <MenuTree
            showActions={true}
            onNodeAction={(action, node) => {
              switch (action) {
                case MenuAction.ADD:
                  handleAdd(node.id)
                  break
                case MenuAction.EDIT:
                  handleEdit(node)
                  break
                case MenuAction.DELETE:
                  handleDelete(node)
                  break
              }
            }}
          />
        </CardContent>
      </Card>

      {/* 菜单表单对话框 */}
      <MenuForm
        action={action}
        visible={visible}
        currentMenu={currentMenu}
        parentId={parentId}
        onClose={closeForm}
        onSuccess={handleFormSuccess}
        onError={handleFormError}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deleteDialog.open}
        menu={deleteDialog.menu}
        loading={deleteDialog.loading}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </div>
  )
}

export default MenuList

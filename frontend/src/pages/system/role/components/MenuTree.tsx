/**
 * 菜单树组件
 * 
 * 用于角色菜单分配的树形选择组件
 * 支持父子节点联动、全选/反选、搜索过滤等功能
 */

import React, { useMemo, useState, useCallback } from 'react'
import { ChevronDown, ChevronRight, Search, Menu, Folder, FileText } from 'lucide-react'
import { Button, Checkbox, Input, Badge } from '@/components/ui'
import { cn } from '@/lib/utils'
import type { Menu as MenuType } from '@/types/api'

export interface MenuTreeProps {
  /** 菜单列表 */
  menus: MenuType[]
  /** 已选中的菜单ID列表 */
  selectedIds: number[]
  /** 选择变化回调 */
  onSelectionChange: (selectedIds: number[]) => void
  /** 样式类名 */
  className?: string
  /** 是否只读 */
  readonly?: boolean
  /** 是否显示权限信息 */
  showPermissionInfo?: boolean
}

interface TreeNode extends MenuType {
  children: TreeNode[]
  level: number
}

/**
 * 获取菜单类型图标
 */
const getMenuTypeIcon = (type: number) => {
  switch (type) {
    case 0: // 目录
      return <Folder className="w-4 h-4 text-blue-500" />
    case 1: // 菜单
      return <FileText className="w-4 h-4 text-green-500" />
    case 2: // 按钮
      return <Menu className="w-4 h-4 text-orange-500" />
    default:
      return <Menu className="w-4 h-4 text-gray-500" />
  }
}

/**
 * 获取菜单类型标签
 */
const getMenuTypeLabel = (type: number) => {
  switch (type) {
    case 0: return '目录'
    case 1: return '菜单'
    case 2: return '按钮'
    default: return '未知'
  }
}

/**
 * 菜单树组件
 */
export const MenuTree: React.FC<MenuTreeProps> = ({
  menus,
  selectedIds,
  onSelectionChange,
  className,
  readonly = false,
  showPermissionInfo = false
}) => {
  const [expandedIds, setExpandedIds] = useState<Set<number>>(new Set())
  const [searchKeyword, setSearchKeyword] = useState('')

  // 构建树形结构（后端已返回树形结构，只需要添加 level 字段）
  const treeData = useMemo(() => {
    if (!menus || menus.length === 0) {
      return []
    }

    const addLevelToTree = (items: MenuType[], level = 0): TreeNode[] => {
      return items.map(item => ({
        ...item,
        level,
        children: item.children ? addLevelToTree(item.children, level + 1) : []
      }))
    }

    return addLevelToTree(menus)
  }, [menus])

  // 过滤树数据（搜索功能）
  const filteredTreeData = useMemo(() => {
    if (!searchKeyword.trim()) {
      return treeData
    }

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce((filtered, node) => {
        const matchesSearch = 
          node.title.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          node.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          (node.path && node.path.toLowerCase().includes(searchKeyword.toLowerCase()))

        const filteredChildren = filterTree(node.children)
        
        if (matchesSearch || filteredChildren.length > 0) {
          filtered.push({
            ...node,
            children: filteredChildren
          })
        }
        
        return filtered
      }, [] as TreeNode[])
    }

    return filterTree(treeData)
  }, [treeData, searchKeyword])

  // 获取所有子节点ID
  const getAllChildIds = useCallback((node: TreeNode): number[] => {
    const ids = [node.id]
    node.children.forEach(child => {
      ids.push(...getAllChildIds(child))
    })
    return ids
  }, [])

  // 获取所有父节点ID
  const getAllParentIds = useCallback((nodeId: number, nodes: TreeNode[]): number[] => {
    const parentIds: number[] = []
    
    const findParent = (id: number, nodeList: TreeNode[]): TreeNode | null => {
      for (const node of nodeList) {
        if (node.children.some(child => child.id === id)) {
          return node
        }
        const found = findParent(id, node.children)
        if (found) return found
      }
      return null
    }

    let currentId = nodeId
    while (true) {
      const parent = findParent(currentId, nodes)
      if (!parent) break
      parentIds.push(parent.id)
      currentId = parent.id
    }

    return parentIds
  }, [])

  // 根据ID查找节点
  const findNodeById = useCallback((id: number, nodes: TreeNode[]): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node
      const found = findNodeById(id, node.children)
      if (found) return found
    }
    return null
  }, [])

  // 处理节点选择
  const handleNodeSelect = useCallback((node: TreeNode, checked: boolean) => {
    if (readonly) return

    let newSelectedIds = [...selectedIds]

    if (checked) {
      // 选中：添加当前节点和所有子节点
      const childIds = getAllChildIds(node)
      childIds.forEach(id => {
        if (!newSelectedIds.includes(id)) {
          newSelectedIds.push(id)
        }
      })

      // 检查是否需要选中父节点
      const parentIds = getAllParentIds(node.id, treeData)
      parentIds.forEach(parentId => {
        const parent = findNodeById(parentId, treeData)
        if (parent) {
          const allChildrenSelected = getAllChildIds(parent).every(id =>
            newSelectedIds.includes(id)
          )
          if (allChildrenSelected && !newSelectedIds.includes(parentId)) {
            newSelectedIds.push(parentId)
          }
        }
      })
    } else {
      // 取消选中：移除当前节点和所有子节点
      const childIds = getAllChildIds(node)
      newSelectedIds = newSelectedIds.filter(id => !childIds.includes(id))

      // 更新父节点状态：只有当父节点的所有子节点都未选中时，才取消选中父节点
      const parentIds = getAllParentIds(node.id, treeData)
      parentIds.forEach(parentId => {
        const parent = findNodeById(parentId, treeData)
        if (parent) {
          const parentAllChildIds = getAllChildIds(parent)
          const parentSelectedChildIds = parentAllChildIds.filter(id => newSelectedIds.includes(id))

          // 只有当父节点的所有子节点都未选中时，才取消选中父节点
          if (parentSelectedChildIds.length === 0 && newSelectedIds.includes(parentId)) {
            newSelectedIds = newSelectedIds.filter(id => id !== parentId)
          }
        }
      })
    }

    onSelectionChange(newSelectedIds)
  }, [selectedIds, onSelectionChange, readonly, getAllChildIds, getAllParentIds, treeData, findNodeById])

  // 切换展开状态
  const toggleExpanded = useCallback((nodeId: number) => {
    setExpandedIds(prev => {
      const newSet = new Set(prev)
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId)
      } else {
        newSet.add(nodeId)
      }
      return newSet
    })
  }, [])

  // 获取所有菜单ID（包括子菜单）
  const getAllMenuIds = useCallback((menuList: MenuType[]): number[] => {
    const ids: number[] = []
    const collectIds = (menus: MenuType[]) => {
      menus.forEach(menu => {
        ids.push(menu.id)
        if (menu.children && menu.children.length > 0) {
          collectIds(menu.children)
        }
      })
    }
    collectIds(menuList)
    return ids
  }, [])

  // 全选/全不选
  const handleSelectAll = useCallback(() => {
    if (readonly) return

    const allIds = getAllMenuIds(menus)
    const isAllSelected = allIds.every(id => selectedIds.includes(id))

    if (isAllSelected) {
      onSelectionChange([])
    } else {
      onSelectionChange(allIds)
    }
  }, [menus, selectedIds, onSelectionChange, readonly, getAllMenuIds])

  // 展开/收起所有
  const handleExpandAll = useCallback(() => {
    const allIds = new Set<number>()
    const collectIds = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        if (node.children.length > 0) {
          allIds.add(node.id)
          collectIds(node.children)
        }
      })
    }
    collectIds(treeData)
    setExpandedIds(allIds)
  }, [treeData])

  const handleCollapseAll = useCallback(() => {
    setExpandedIds(new Set())
  }, [])

  // 计算节点选中状态
  const getNodeCheckState = useCallback((node: TreeNode): 'checked' | 'unchecked' | 'indeterminate' => {
    const allChildIds = getAllChildIds(node)
    const selectedChildIds = allChildIds.filter(id => selectedIds.includes(id))

    if (selectedChildIds.length === 0) return 'unchecked'
    if (selectedChildIds.length === allChildIds.length) return 'checked'
    return 'indeterminate'
  }, [selectedIds, getAllChildIds])

  // 获取缩进样式
  const getIndentStyle = useCallback((level: number) => {
    return { paddingLeft: `${level * 16}px` }
  }, [])

  // 渲染树节点
  const renderTreeNode = useCallback((node: TreeNode) => {
    const checkState = getNodeCheckState(node)
    const isSelected = checkState === 'checked'
    const isIndeterminate = checkState === 'indeterminate'
    const isExpanded = expandedIds.has(node.id)
    const hasChildren = node.children.length > 0

    return (
      <div key={node.id} className="menu-tree-node">
        <div
          className="flex items-center py-2 px-2 rounded-md hover:bg-muted/50 transition-colors"
          style={getIndentStyle(node.level)}
        >
          {/* 展开/收起按钮 */}
          <div className="w-6 h-6 flex items-center justify-center">
            {hasChildren ? (
              <Button
                variant="ghost"
                size="sm"
                className="w-4 h-4 p-0"
                onClick={() => toggleExpanded(node.id)}
              >
                {isExpanded ? (
                  <ChevronDown className="w-3 h-3" />
                ) : (
                  <ChevronRight className="w-3 h-3" />
                )}
              </Button>
            ) : null}
          </div>

          {/* 复选框 */}
          <Checkbox
            checked={isSelected}
            indeterminate={isIndeterminate}
            onCheckedChange={(checked) => {
              // 当处于半选中状态时，点击应该选中所有子节点
              if (isIndeterminate) {
                handleNodeSelect(node, true)
              } else {
                handleNodeSelect(node, checked as boolean)
              }
            }}
            disabled={readonly}
            className="mr-3"
          />

          {/* 菜单图标 */}
          <div className="mr-2">
            {getMenuTypeIcon(node.type)}
          </div>

          {/* 菜单信息 */}
          <div className="flex-1 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{node.title}</span>
              <Badge variant="outline" className="text-xs">
                {getMenuTypeLabel(node.type)}
              </Badge>

              {/* 权限信息显示 */}
              {showPermissionInfo && node.type === 2 && node.permissionCode && (
                <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                  权限: {node.permissionCode}
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {node.path && (
                <span className="text-xs text-muted-foreground font-mono">
                  {node.path}
                </span>
              )}

              {/* 权限编码显示 */}
              {showPermissionInfo && node.permissionCode && node.type === 2 && (
                <span className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                  {node.permissionCode}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <div>
            {node.children.map(renderTreeNode)}
          </div>
        )}
      </div>
    )
  }, [selectedIds, expandedIds, handleNodeSelect, toggleExpanded, getAllChildIds, readonly, getNodeCheckState, getIndentStyle])

  const allIds = getAllMenuIds(menus)
  const isAllSelected = allIds.length > 0 && allIds.every(id => selectedIds.includes(id))
  const isIndeterminate = selectedIds.length > 0 && !isAllSelected

  return (
    <div className={cn("menu-tree h-full flex flex-col", className)}>
      {/* 搜索和操作栏 - 固定区域 */}
      <div className="flex-shrink-0 space-y-3 mb-4">
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="搜索菜单名称、路径..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={isAllSelected}
              indeterminate={isIndeterminate}
              onCheckedChange={handleSelectAll}
              disabled={readonly}
            />
            <span className="text-sm font-medium">
              全选 ({selectedIds.length}/{allIds.length})
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExpandAll}
            >
              展开全部
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCollapseAll}
            >
              收起全部
            </Button>
          </div>
        </div>
      </div>

      {/* 树形结构 - 可滚动区域，占用剩余空间 */}
      <div className="flex-1 border rounded-lg overflow-hidden">
        <div className="h-full overflow-y-auto p-2">
          {filteredTreeData.length > 0 ? (
            <div className="space-y-1">
              {filteredTreeData.map(renderTreeNode)}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              {searchKeyword ? '未找到匹配的菜单' : '暂无菜单数据'}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default MenuTree

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>ef<PERSON>, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import UserSyncWizardSimple from '@/components/user-sync/UserSyncWizard';
import { PagePermissionWrapper } from '@/components/auth/PermissionWrapper.tsx';

/**
 * 用户同步页面
 * 提供分步骤的用户同步功能
 */
const UserSync: React.FC = () => {
  const navigate = useNavigate();

  const handleComplete = () => {
    // 同步完成后跳转回用户列表页面
    navigate('/system/user', { 
      state: { 
        message: '用户同步操作已完成，请刷新列表查看结果' 
      } 
    });
  };

  const handleCancel = () => {
    // 取消同步，返回用户列表页面
    navigate('/system/user');
  };

  const handleRefresh = () => {
    // 刷新页面重新开始同步
    window.location.reload();
  };

  return (
    <PagePermissionWrapper permissions={['system:user:sync']}>
      <div className="min-h-screen bg-gray-50/50">
        {/* 页面头部 */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/system/user')}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>返回用户列表</span>
                </Button>
                <div className="h-6 w-px bg-gray-300" />
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">用户同步</h1>
                  <p className="text-sm text-gray-500">从 VIM 系统同步用户数据</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>重新开始</span>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 页面内容 */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 功能说明 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <RefreshCw className="w-5 h-5" />
                <span>用户同步功能</span>
              </CardTitle>
              <CardDescription>
                新的分步骤用户同步功能，提供更好的控制和反馈体验
              </CardDescription>
            </CardHeader>
          </Card>

          {/* 同步向导 */}
          <UserSyncWizardSimple
            onComplete={handleComplete}
            onCancel={handleCancel}
          />
        </div>

        {/* 页面底部信息 */}
        <div className="bg-white border-t mt-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="text-right">
                <p>支持批量处理，建议单次同步不超过 1000 个用户</p>
                <p>大批量数据建议使用异步模式</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PagePermissionWrapper>
  );
};

export default UserSync;

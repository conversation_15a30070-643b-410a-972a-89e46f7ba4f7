/**
 * 用户表格组件
 *
 * 显示用户列表数据，支持排序、选择、操作等功能
 * 统一重构版本 - 完整的权限控制实现
 */

import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  // Button,
  Checkbox,
  Badge,
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/components/ui'
import { DataPagination } from '../../../../components/common/DataPagination'
import { Edit, Shield, Trash2, ArrowUpDown, ArrowUp, ArrowDown, Key, UserCheck, UserX } from 'lucide-react'
import { ActionPermissionButton } from '@/components/auth/PermissionWrapper.tsx'
import type { User } from '@/types'
import { getUserStatusLabel, UserStatus } from '@/types'

// 格式化时间戳（支持秒级和毫秒级）
const formatDateTime = (timestamp: string | number) => {
  if (!timestamp) return '-'

  let date: Date
  if (typeof timestamp === 'string') {
    // 如果是字符串，尝试解析
    const num = parseInt(timestamp)
    if (isNaN(num)) {
      // 如果不是数字字符串，按ISO格式解析
      date = new Date(timestamp)
    } else {
      // 如果是数字字符串，按时间戳处理
      date = new Date(num < 10000000000 ? num * 1000 : num)
    }
  } else {
    // 如果是数字，判断是秒级还是毫秒级时间戳
    date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp)
  }

  if (isNaN(date.getTime())) return '-'

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 渲染部门信息
const renderDeptInfo = (user: User) => {
  if (!user.deptName) {
    return <span className="text-muted-foreground">-</span>
  }

  return (
    <div className="max-w-40">
      <Badge variant="outline" className="text-xs" title={user.deptName}>
        {user.deptName}
      </Badge>
      {user.deptNames && user.deptNames.length > 1 && (
        <div className="text-xs text-muted-foreground mt-1" title={user.deptNames.join(', ')}>
          +{user.deptNames.length - 1}个部门
        </div>
      )}
    </div>
  )
}

// 渲染角色信息
const renderRoleInfo = (user: User) => {
  if (!user.roleNames || user.roleNames.length === 0) {
    return <span className="text-muted-foreground">-</span>
  }

  return (
    <div className="max-w-48">
      <div className="flex flex-wrap gap-1">
        {user.roleNames.slice(0, 2).map((roleName, index) => (
          <Badge key={index} variant="secondary" className="text-xs" title={roleName}>
            {roleName.length > 8 ? `${roleName.slice(0, 8)}...` : roleName}
          </Badge>
        ))}
        {user.roleNames.length > 2 && (
          <Badge
            variant="outline"
            className="text-xs"
            title={user.roleNames.slice(2).join(', ')}
          >
            +{user.roleNames.length - 2}
          </Badge>
        )}
      </div>
    </div>
  )
}

export interface UserTableProps {
  data: User[]
  loading: boolean
  selectedIds: number[]
  onSelectionChange: (ids: number[]) => void
  onEdit: (user: User) => void
  onDelete: (user: User) => void
  onResetPassword: (user: User) => void
  onAssignRoles: (user: User) => void
  onToggleStatus: (user: User) => void
  pagination: {
    current: number
    pageSize: number
    total: number
    onChange: (page: number, pageSize: number) => void
  }
  sortConfig?: {
    field: string
    order: 'asc' | 'desc'
  }
  onSort?: (field: string) => void
}

/**
 * 用户表格组件
 */
const UserTable: React.FC<UserTableProps> = ({
  data,
  loading,
  selectedIds,
  onSelectionChange,
  onEdit,
  onDelete,
  onResetPassword,
  onAssignRoles,
  onToggleStatus,
  pagination,
  sortConfig,
  onSort
}) => {
  // 处理全选
  const isAllSelected = data.length > 0 && selectedIds.length === data.length
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < data.length

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(data.map(item => item.id))
    } else {
      onSelectionChange([])
    }
  }

  // 处理单选
  const handleSelectItem = (id: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, id])
    } else {
      onSelectionChange(selectedIds.filter(selectedId => selectedId !== id))
    }
  }

  // 处理排序
  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field)
    }
  }

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (!sortConfig || sortConfig.field !== field) {
      return <ArrowUpDown className="w-4 h-4 ml-1 opacity-50" />
    }
    
    return sortConfig.order === 'asc' 
      ? <ArrowUp className="w-4 h-4 ml-1" />
      : <ArrowDown className="w-4 h-4 ml-1" />
  }

  // 处理页面大小变化
  const handlePageSizeChange = (newPageSize: number) => {
    console.log('📏 页面大小变更:', newPageSize)
    pagination.onChange(1, newPageSize) // 切换页面大小时回到第一页
  }

  // 获取用户头像显示名称
  const getAvatarName = (user: User) => {
    return user.realName || user.nickname || user.username
  }

  // 获取用户头像首字母
  const getAvatarInitial = (user: User) => {
    const name = getAvatarName(user)
    return name.charAt(0).toUpperCase()
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-md border overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">选择</TableHead>
                <TableHead className="w-20">头像</TableHead>
                <TableHead className="w-32">用户名</TableHead>
                <TableHead className="w-32">昵称</TableHead>
                <TableHead className="w-32">真实姓名</TableHead>
                <TableHead className="w-40">邮箱</TableHead>
                <TableHead className="w-32">手机号</TableHead>
                <TableHead className="w-20">性别</TableHead>
                <TableHead className="w-20">状态</TableHead>
                <TableHead className="w-24">用户类型</TableHead>
                <TableHead className="w-40">创建时间</TableHead>
                <TableHead className="w-48">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-8 w-8 bg-muted animate-pulse rounded-full" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 bg-muted animate-pulse rounded" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground">暂无用户数据</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 表格 - 添加水平滚动 */}
      <div className="rounded-md border overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="选择所有用户"
                  data-state={isIndeterminate ? "indeterminate" : isAllSelected ? "checked" : "unchecked"}
                />
              </TableHead>
              <TableHead className="w-20">头像</TableHead>
              <TableHead
                className="w-32 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('username')}
              >
                <div className="flex items-center">
                  用户名
                  {renderSortIcon('username')}
                </div>
              </TableHead>
              <TableHead className="w-32">昵称</TableHead>
              <TableHead className="w-32">真实姓名</TableHead>
              <TableHead className="w-32">手机号</TableHead>
              <TableHead className="w-40">部门</TableHead>
              <TableHead className="w-48">角色</TableHead>
              <TableHead
                className="w-20 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  状态
                  {renderSortIcon('status')}
                </div>
              </TableHead>
              <TableHead
                className="w-40 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('createTime')}
              >
                <div className="flex items-center">
                  创建时间
                  {renderSortIcon('createTime')}
                </div>
              </TableHead>
              <TableHead className="w-48">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              // 加载状态
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><div className="h-4 w-4 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-8 w-8 bg-muted animate-pulse rounded-full" /></TableCell>
                  <TableCell><div className="h-4 w-20 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 w-20 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 w-16 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 w-20 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 w-16 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 w-24 bg-muted animate-pulse rounded" /></TableCell>
                  <TableCell><div className="h-4 w-32 bg-muted animate-pulse rounded" /></TableCell>
                </TableRow>
              ))
            ) : data.length === 0 ? (
              // 空状态
              <TableRow>
                <TableCell colSpan={11} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-muted-foreground">
                    <div className="text-4xl mb-2">👤</div>
                    <div className="text-lg font-medium mb-1">暂无用户数据</div>
                    <div className="text-sm">请尝试调整搜索条件或添加新用户</div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((user) => (
              <TableRow key={user.id} className="hover:bg-muted/50">
                <TableCell>
                  <Checkbox
                    checked={selectedIds.includes(user.id)}
                    onCheckedChange={(checked) => handleSelectItem(user.id, checked as boolean)}
                    aria-label={`选择用户 ${user.username}`}
                  />
                </TableCell>
                <TableCell>
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={user.avatar} alt={getAvatarName(user)} />
                    <AvatarFallback className="text-xs">
                      {getAvatarInitial(user)}
                    </AvatarFallback>
                  </Avatar>
                </TableCell>
                <TableCell className="font-medium">{user.username}</TableCell>
                <TableCell>{user.nickname || '-'}</TableCell>
                <TableCell>{user.realName || '-'}</TableCell>
                <TableCell>{user.phone || '-'}</TableCell>
                <TableCell>
                  {renderDeptInfo(user)}
                </TableCell>
                <TableCell>
                  {renderRoleInfo(user)}
                </TableCell>
                <TableCell>
                  <Badge variant={user.status === UserStatus.ENABLED ? 'default' : 'secondary'}>
                    {getUserStatusLabel(user.status)}
                  </Badge>
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {formatDateTime(user.createTime)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    {/* 编辑按钮 - 蓝色 */}
                    <ActionPermissionButton
                      module="user"
                      action="edit"
                      config={{
                        text: '',
                        icon: Edit,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onEdit(user),
                        title: '编辑用户',
                        className: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                      }}
                    />

                    {/* 分配角色按钮 - 紫色 */}
                    <ActionPermissionButton
                      module="user"
                      action="edit"
                      config={{
                        text: '',
                        icon: Shield,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onAssignRoles(user),
                        title: '分配角色',
                        className: 'text-purple-600 hover:text-purple-700 hover:bg-purple-50'
                      }}
                    />

                    {/* 重置密码按钮 - 橙色 */}
                    <ActionPermissionButton
                      module="user"
                      action="resetPassword"
                      config={{
                        text: '',
                        icon: Key,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onResetPassword(user),
                        title: '重置密码',
                        className: 'text-orange-600 hover:text-orange-700 hover:bg-orange-50'
                      }}
                    />

                    {/* 状态切换按钮 - 动态颜色 */}
                    <ActionPermissionButton
                      module="user"
                      action="edit"
                      config={{
                        text: '',
                        icon: user.status === UserStatus.ENABLED ? UserX : UserCheck,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onToggleStatus(user),
                        title: user.status === UserStatus.ENABLED ? '禁用用户' : '启用用户',
                        className: user.status === UserStatus.ENABLED
                          ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                          : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                      }}
                    />

                    {/* 删除按钮 - 红色 */}
                    <ActionPermissionButton
                      module="user"
                      action="delete"
                      config={{
                        text: '',
                        icon: Trash2,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onDelete(user),
                        title: '删除用户',
                        className: 'text-red-600 hover:text-red-700 hover:bg-red-50'
                      }}
                    />
                  </div>
                </TableCell>
              </TableRow>
            )))}
          </TableBody>
        </Table>
      </div>
      
      {/* 统一分页组件 */}
      <DataPagination
        current={pagination.current}
        pageSize={pagination.pageSize}
        total={pagination.total}
        onPageChange={(page) => {
          console.log('📄 页码点击:', page, '当前页面大小:', pagination.pageSize)
          pagination.onChange(page, pagination.pageSize || 10)
        }}
        onPageSizeChange={handlePageSizeChange}
        showSizeChanger={true}
        showTotal={true}
        pageSizeOptions={[10, 20, 50, 100]}
      />
    </div>
  )
}

export default UserTable

/**
 * 主播统计组件
 * 
 * 显示选中主播的详细统计数据，包括：
 * - 基础统计数据（充值、消费、用户数等）
 * - 首充统计数据（首充率、首充金额等）
 * - 支持时间范围筛选
 */

import React from 'react'
import { Button } from '@/components/ui'
import { RefreshCw, Users, DollarSign, Target } from 'lucide-react'
import { DateRangePicker } from '@/components/ui'
import { PermissionStatsCard } from '@/components/operations/PermissionStatsCard'
import { useAnchorStats, useStatsFormatter } from '../hooks/useAnchorStats'
import { OperationsUtils } from '@/services/operations'
import type { AnchorListResponse } from '../types/operations'
import { addDays, startOfDay, endOfDay } from 'date-fns'
import type { DateRange } from 'react-day-picker'
import { debugAnchorData, validateApiParams } from '@/utils/anchorDebug'
import { PERMISSIONS } from '@/constants/permissions'

export interface AnchorStatsProps {
  /** 选中的主播 */
  anchor: AnchorListResponse | null
  /** 时间范围 */
  dateRange?: DateRange
  /** 时间范围变化回调 */
  onDateRangeChange?: (range: DateRange | undefined) => void
  /** 返回回调 */
  onBack?: () => void
  /** 类名 */
  className?: string
}

// StatsCard组件已替换为PermissionStatsCard，提供权限控制功能

/**
 * 主播统计组件
 */
export const AnchorStats: React.FC<AnchorStatsProps> = ({
  anchor,
  dateRange,
  onDateRangeChange,
  className
}) => {
  const {
    statsData,
    firstRechargeData,
    statsLoading,
    firstRechargeLoading,
    error,
    loadAllStats,
    refreshStats
  } = useAnchorStats()
  
  const { formatStatsCards, formatFirstRechargeCards } = useStatsFormatter()
  
  // 处理时间范围变化
  const handleDateRangeChange = (range: DateRange | undefined) => {
    onDateRangeChange?.(range)

    if (anchor && range?.from && range?.to) {
      // 使用调试工具验证主播数据
      const validation = debugAnchorData(anchor, '时间范围变化')
      if (!validation.isValid) {
        return
      }

      const startTime = OperationsUtils.dateToTimestamp(range.from)
      const endTime = OperationsUtils.dateToTimestamp(range.to)

      // 验证API参数
      const paramValidation = validateApiParams(anchor.id, startTime, endTime)
      if (!paramValidation.isValid) {
        return
      }

      loadAllStats(anchor.id, startTime, endTime)
    }
  }
  
  // 处理刷新
  const handleRefresh = () => {
    refreshStats()
  }
  
  // 当主播变化时加载数据
  React.useEffect(() => {
    if (anchor) {
      // 使用调试工具验证主播数据
      const validation = debugAnchorData(anchor, '主播变化')
      if (!validation.isValid) {
        return
      }

      // 总是使用时间范围，如果没有设置则使用默认的30天范围
      const effectiveDateRange = dateRange || {
        from: startOfDay(addDays(new Date(), -29)),
        to: endOfDay(new Date()),
      }

      if (effectiveDateRange.from && effectiveDateRange.to) {
        const startTime = OperationsUtils.dateToTimestamp(effectiveDateRange.from)
        const endTime = OperationsUtils.dateToTimestamp(effectiveDateRange.to)

        // 验证API参数
        const paramValidation = validateApiParams(anchor.id, startTime, endTime)
        if (!paramValidation.isValid) {
          return
        }

        loadAllStats(anchor.id, startTime, endTime)
      }
    }
  }, [anchor, loadAllStats])
  
  if (!anchor) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        请选择一个主播查看统计数据
      </div>
    )
  }
  
  const statsCards = formatStatsCards(statsData)
  const firstRechargeCards = formatFirstRechargeCards(firstRechargeData)

  // 转换为权限控制的卡片数据
  const convertToPermissionCards = (cards: any[]) => {
    return cards.map((card, index) => {
      // 根据卡片标题确定权限要求
      let permissions: string[] = []
      let noPermissionBehavior: 'hide' | 'mask' | 'placeholder' = 'placeholder'

      if (card.title.includes('充值') || card.title.includes('金额') || card.title.includes('收入')) {
        permissions = [PERMISSIONS.OPERATIONS.STATS.VIEW_FINANCIAL]
        noPermissionBehavior = 'mask'
      } else if (card.title.includes('利润')) {
        permissions = [PERMISSIONS.OPERATIONS.STATS.VIEW_PROFIT]
        noPermissionBehavior = 'mask'
      } else {
        permissions = [PERMISSIONS.OPERATIONS.VIEW]
        noPermissionBehavior = 'placeholder'
      }

      return {
        key: `${card.title}-${index}`,
        title: card.title,
        value: card.value,
        description: card.description,
        icon: <DollarSign className="h-4 w-4" />,
        permissions,
        noPermissionBehavior,
        noPermissionMessage: `您没有查看${card.title}的权限`,
        trend: card.trend
      }
    })
  }

  const permissionStatsCards = convertToPermissionCards(statsCards)
  const permissionFirstRechargeCards = convertToPermissionCards(firstRechargeCards)

  const loading = statsLoading || firstRechargeLoading
  
  return (
    <div className={className}>
      {/* 头部信息 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">主播统计</h2>
          <p className="text-muted-foreground">
            {anchor.nickname} ({anchor.username})
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* 时间范围选择器 */}
          <DateRangePicker
            enableTime={true}
            timeFormat="HH:mm:ss"
            onDateChange={handleDateRangeChange}
          />

          {/* 刷新按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>
      
      {/* 错误提示 */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md mb-6">
          {error}
        </div>
      )}
      
      {/* 基础统计数据 */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <DollarSign className="h-5 w-5 mr-2" />
          基础统计
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {permissionStatsCards.map((card) => (
            <PermissionStatsCard
              key={card.key}
              title={card.title}
              value={card.value}
              description={card.description}
              icon={card.icon}
              permissions={card.permissions}
              noPermissionBehavior={card.noPermissionBehavior}
              noPermissionMessage={card.noPermissionMessage}
              trend={card.trend}
              loading={loading}
              className="border-primary bg-primary/5"
            />
          ))}
        </div>
      </div>
      
      {/* 首充统计数据 */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Target className="h-5 w-5 mr-2" />
          首充统计
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {permissionFirstRechargeCards.map((card) => (
            <PermissionStatsCard
              key={card.key}
              title={card.title}
              value={card.value}
              description={card.description}
              icon={<Users className="h-4 w-4" />}
              permissions={card.permissions}
              noPermissionBehavior={card.noPermissionBehavior}
              noPermissionMessage={card.noPermissionMessage}
              trend={card.trend}
              loading={loading}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

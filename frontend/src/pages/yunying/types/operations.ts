/**
 * 运营模块类型定义
 * 
 * 基于后端DTO类定义，确保前后端类型一致性
 */

import { z } from 'zod'
// 定义与后端一致的分页请求接口
export interface PageRequest {
  pageNum: number
  pageSize: number
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
}

// 定义与后端一致的分页结果接口
export interface PageResult<T> {
  records: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

/**
 * 用户身份类型枚举
 */
export enum UserIdentity {
  /** 普通用户 */
  USER = 1,
  /** 线上主播 */
  ONLINE_ANCHOR = 2,
  /** 线下主播 */
  OFFLINE_ANCHOR = 3
}

/**
 * 用户状态枚举
 */
export enum UserState {
  /** 正常 */
  NORMAL = 1,
  /** 禁用 */
  DISABLED = 2
}

/**
 * 实名认证状态枚举
 */
export enum AuthStatus {
  /** 未实名 */
  NOT_AUTH = 0,
  /** 已实名 */
  AUTHED = 1
}

/**
 * 充值订单状态枚举
 */
export enum RechargeState {
  /** 未支付 */
  UNPAID = 1,
  /** 已支付 */
  PAID = 2,
  /** 已支付但回调异常 */
  PAID_CALLBACK_ERROR = 3
}

/**
 * 消费类型枚举
 */
export enum ConsumeType {
  /** 购买道具 */
  BUY_ITEM = '购买道具',
  /** 开箱消费 */
  OPEN_BOX = '开箱消费',
  /** 锻造消费 */
  FORGE = '锻造消费',
  /** 其他消费 */
  OTHER = '其他消费'
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  /** 微信支付 */
  WECHAT = '微信支付',
  /** 支付宝 */
  ALIPAY = '支付宝',
  /** 其他支付方式 */
  OTHER = '其他'
}

// ==================== 请求类型定义 ====================

/**
 * 主播查询请求
 */
export interface AnchorQueryRequest extends PageRequest {
  /** 主播昵称 */
  nickname?: string
  /** 主播状态 */
  status?: UserState
  /** 身份类型 */
  identity?: UserIdentity
  /** 注册开始时间（时间戳） */
  registerStartTime?: number
  /** 注册结束时间（时间戳） */
  registerEndTime?: number
  /** 是否实名认证 */
  isAuth?: AuthStatus
  /** 手机号 */
  phone?: string
  /** 上级用户名或昵称 */
  managerName?: string
}

/**
 * 下级用户查询请求
 */
export interface SubUserQueryRequest extends PageRequest {
  /** 用户昵称 */
  nickname?: string
  /** 用户名 */
  username?: string
  /** 手机号 */
  phone?: string
  /** 账号状态 */
  state?: UserState
  /** 是否实名认证 */
  isAuth?: AuthStatus
  /** 注册开始时间（时间戳） */
  registerStartTime?: number
  /** 注册结束时间（时间戳） */
  registerEndTime?: number
  /** 是否已首充 */
  hasFirstRecharge?: boolean
  /** 最小充值金额 */
  minRechargeAmount?: number
  /** 最大充值金额 */
  maxRechargeAmount?: number
}

/**
 * 消费查询请求
 */
export interface ConsumeQueryRequest extends PageRequest {
  /** 订单号 */
  orderId?: string
  /** 消费开始时间（时间戳） */
  startTime?: number
  /** 消费结束时间（时间戳） */
  endTime?: number
  /** 最小消费金额 */
  minAmount?: number
  /** 最大消费金额 */
  maxAmount?: number
  /** 消费说明（模糊查询） */
  info?: string
}

/**
 * 充值查询请求
 */
export interface RechargeQueryRequest extends PageRequest {
  /** 充值订单号 */
  orderId?: string
  /** 三方订单号 */
  payId?: string
  /** 充值开始时间（时间戳） */
  startTime?: number
  /** 充值结束时间（时间戳） */
  endTime?: number
  /** 最小充值金额 */
  minAmount?: number
  /** 最大充值金额 */
  maxAmount?: number
  /** 订单状态 */
  state?: RechargeState
  /** 是否首充 */
  isFirstRecharge?: boolean
}

// ==================== 响应类型定义 ====================

/**
 * 主播列表响应
 */
export interface AnchorListResponse {
  /** 主播ID（vim_user表的ID，Integer类型） */
  id: number
  /** 主播昵称 */
  nickname: string
  /** 用户名 */
  username: string
  /** 手机号 */
  phone: string
  /** 头像URL */
  userimage?: string
  /** 账号状态 */
  state: UserState
  /** 身份类型 */
  identity: UserIdentity
  /** 是否实名认证 */
  isauth: AuthStatus
  /** 当前货币余额 */
  coin: number
  /** 当前钥匙数量 */
  key: string
  /** 下级用户数量 */
  subUserCount: number
  /** 注册时间（时间戳） */
  createTime: number
  /** 最后登录时间（时间戳） */
  lastLoginTime?: number
  /** 最后登录IP */
  lastLoginIp?: string
  /** 邀请码 */
  inviteCode: string
  /** 用户等级 */
  level: number
  /** 用户经验 */
  exp: number
  /** 上级用户名称 */
  managerName: string
  /** 上级用户id */
  managerId: number
}

/**
 * 主播统计数据响应
 */
export interface AnchorStatsResponse {
  /** 总充值金额 */
  totalRecharge: number
  /** 总消费金额 */
  totalConsume: number
  /** 用户总数 */
  userCount: number
  /** 指定时间区间内新增用户数 */
  periodNewUserCount: number
  /** 指定时间区间内新邀请的下级用户总数 */
  periodNewInviteCount?: number
  /** 总待发货金额 */
  totalClaimAmount: number
  /** 总实际发货金额 */
  totalShippedAmount: number
  /** 背包总价值 */
  totalBackpackAmount: number
  /** 时间区间内下级用户总充值金额 */
  periodTotalRecharge: number
  /** 总流水 */
  totalTurnover: number
  /** 利润比 */
  profitRatio: number
  /** 实际利润金额 */
  actualProfit: number
  /** 查询开始时间（时间戳） */
  startTime?: number
  /** 查询结束时间（时间戳） */
  endTime?: number
}

/**
 * 首充统计响应
 */
export interface FirstRechargeStatsResponse {
  /** 首充用户总数 */
  firstRechargeUserCount: number
  /** 下级用户总数 */
  totalSubUserCount: number
  /** 首充转化率 */
  firstRechargeConversionRate: number
  /** 首充总金额 */
  totalFirstRechargeAmount: number
  /** 平均首充金额 */
  avgFirstRechargeAmount: number
  /** 时间区间内首充用户数 */
  periodFirstRechargeUserCount: number
  /** 时间区间内首充总金额 */
  periodFirstRechargeAmount: number
  /** 时间区间内平均首充金额 */
  periodAvgFirstRechargeAmount: number
  /** 查询开始时间（时间戳） */
  startTime?: number
  /** 查询结束时间（时间戳） */
  endTime?: number
}

/**
 * 下级用户响应
 */
export interface SubUserResponse {
  /** 用户ID */
  id: number
  /** 用户昵称 */
  nickname: string
  /** 用户名 */
  username: string
  /** 手机号 */
  phone: string
  /** 头像URL */
  userimage: string
  /** 账号状态 */
  state: UserState
  /** 是否实名认证 */
  isauth: AuthStatus
  /** 当前货币余额 */
  coin: number
  /** 当前钥匙数量 */
  key: number
  /** 注册时间（时间戳） */
  createTime: number
  /** 最后登录时间（时间戳） */
  lastLoginTime: number
  /** 累计充值金额 */
  totalRecharge: number
  /** 累计消费金额 */
  totalConsume: number
  /** 首充时间（时间戳） */
  firstRechargeTime?: number
  /** 首充金额 */
  firstRechargeAmount: number
  /** 是否已首充 */
  hasFirstRecharge: boolean
  /** 用户等级 */
  level: number
  /** 用户经验 */
  exp: number
  /** 邀请用户ID */
  inviteUser: number
}

/**
 * 消费详情响应
 */
export interface ConsumeDetailResponse {
  /** 订单ID */
  id: string
  /** 用户ID */
  uid: number
  /** 用户昵称 */
  nickname: string
  /** 消费金额 */
  amount: number
  /** 消费后用户余额 */
  balance: number
  /** 消费时间（时间戳） */
  time: number
  /** 消费说明 */
  info: string
  /** 消费类型 */
  consumeType: ConsumeType
  /** 是否首充后的消费 */
  isAfterFirstRecharge: boolean
}

/**
 * 充值详情响应
 */
export interface RechargeDetailResponse {
  /** 充值订单ID */
  id: string
  /** 三方订单号 */
  payid: string
  /** 用户ID */
  uid: number
  /** 用户昵称 */
  nickname: string
  /** 充值金额 */
  amount: number
  /** 获得代币数量 */
  coin: number
  /** 订单状态 */
  state: RechargeState
  /** 订单状态描述 */
  stateDesc: string
  /** 创建时间（时间戳） */
  createTime: number
  /** 更新时间（时间戳） */
  updateTime: number
  /** 是否首充 */
  isFirstRecharge: boolean
  /** 支付方式 */
  paymentMethod: PaymentMethod
}

// ==================== Zod 验证模式 ====================

/**
 * 主播查询请求验证模式
 */
export const AnchorQueryRequestSchema = z.object({
  pageNum: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  nickname: z.string().optional(),
  status: z.nativeEnum(UserState).optional(),
  identity: z.nativeEnum(UserIdentity).optional(),
  registerStartTime: z.number().optional(),
  registerEndTime: z.number().optional(),
  isAuth: z.nativeEnum(AuthStatus).optional(),
  phone: z.string().optional()
})

/**
 * 下级用户查询请求验证模式
 */
export const SubUserQueryRequestSchema = z.object({
  pageNum: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  nickname: z.string().optional(),
  username: z.string().optional(),
  phone: z.string().optional(),
  state: z.nativeEnum(UserState).optional(),
  isAuth: z.nativeEnum(AuthStatus).optional(),
  registerStartTime: z.number().optional(),
  registerEndTime: z.number().optional(),
  hasFirstRecharge: z.boolean().optional(),
  minRechargeAmount: z.number().min(0).optional(),
  maxRechargeAmount: z.number().min(0).optional()
})

/**
 * 消费查询请求验证模式
 */
export const ConsumeQueryRequestSchema = z.object({
  pageNum: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  orderId: z.string().optional(),
  startTime: z.number().optional(),
  endTime: z.number().optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  info: z.string().optional()
})

/**
 * 充值查询请求验证模式
 */
export const RechargeQueryRequestSchema = z.object({
  pageNum: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  orderId: z.string().optional(),
  payId: z.string().optional(),
  startTime: z.number().optional(),
  endTime: z.number().optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  state: z.nativeEnum(RechargeState).optional(),
  isFirstRecharge: z.boolean().optional()
})

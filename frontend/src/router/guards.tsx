import React, { useEffect } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useMemoizedFn, useThrottleFn, usePrevious, useUpdateEffect } from 'ahooks'
import { useAuthStore, useAppStore, usePermission } from '../stores'
import { ROUTES, PERMISSIONS, ROLES } from '../constants'

/**
 * 认证守卫组件
 */
interface AuthGuardProps {
  children: React.ReactNode
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation()
  const { isAuthenticated, checkAuth, loading, user, permissions, roles } = useAuthStore()
  const { setGlobalLoading } = useAppStore()

  // 使用ahooks的usePrevious跟踪前一个状态，避免重复处理
  const prevIsAuthenticated = usePrevious(isAuthenticated)
  const prevPathname = usePrevious(location.pathname)
  const prevLoading = usePrevious(loading)

  // 使用ahooks的useMemoizedFn确保函数引用永远不变
  const memoizedSetGlobalLoading = useMemoizedFn(setGlobalLoading)
  const memoizedCheckAuth = useMemoizedFn(checkAuth)

  // 使用ahooks的useThrottleFn节流checkAuth调用，防止频繁调用
  const { run: throttledCheckAuth } = useThrottleFn(
    useMemoizedFn(() => {
      memoizedCheckAuth()
    }),
    {
      wait: 3000, // 增加到3秒，进一步减少调用频率
      leading: true, // 立即执行第一次
      trailing: false // 不在结尾执行
    }
  )

  // 使用ahooks的useUpdateEffect处理认证检查，跳过首次渲染
  useUpdateEffect(() => {
    // 只有当认证状态或路径真正改变时才检查
    const authChanged = prevIsAuthenticated !== isAuthenticated
    const pathChanged = prevPathname !== location.pathname

    // 增加更严格的条件检查
    if ((authChanged || pathChanged) &&
        !isAuthenticated &&
        location.pathname !== ROUTES.LOGIN &&
        !loading) { // 不在加载状态时才检查
      console.log('认证状态或路径变化，需要检查:', {
        authChanged,
        pathChanged,
        pathname: location.pathname,
        loading
      })
      throttledCheckAuth()
    }
  }, [isAuthenticated, location.pathname, loading])

  // 使用防抖处理全局加载状态，避免频繁更新
  const { run: debouncedSetGlobalLoading } = useThrottleFn(
    useMemoizedFn((newLoading: boolean) => {
      console.log('设置全局加载状态:', newLoading)
      memoizedSetGlobalLoading(newLoading)
    }),
    {
      wait: 100, // 100ms防抖
      leading: true,
      trailing: true
    }
  )

  // 使用ahooks的useUpdateEffect处理全局加载状态，避免首次渲染时的设置
  useUpdateEffect(() => {
    // 只有当loading状态真正改变时才更新
    if (prevLoading !== loading) {
      console.log('加载状态变化:', { from: prevLoading, to: loading })
      debouncedSetGlobalLoading(loading)
    }
  }, [loading])

  // 如果正在加载，显示全局加载状态
  if (loading) {
    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-card rounded-lg shadow-lg p-8 flex flex-col items-center space-y-6 min-w-[200px]">
          <div className="text-4xl">⚡</div>
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary border-t-transparent absolute top-0 left-0"></div>
          </div>
          <div className="text-center">
            <h3 className="text-xl font-semibold text-foreground mb-2">jCloud</h3>
            <p className="text-sm text-muted-foreground">验证身份中...</p>
          </div>
        </div>
      </div>
    )
  }

  // 如果未认证且不在登录页，重定向到登录页
  if (!isAuthenticated && location.pathname !== ROUTES.LOGIN) {
    console.log('未认证，重定向到登录页')
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />
  }

  // 如果已认证且在登录页，重定向到有权限的默认页面
  if (isAuthenticated && location.pathname === ROUTES.LOGIN) {
    // 优先跳转到来源页面，如果没有来源页面则跳转到用户有权限的默认页面
    let targetPath = location.state?.from?.pathname

    // 避免循环重定向
    if (!targetPath || targetPath === ROUTES.LOGIN || targetPath === location.pathname) {
      // 首先检查用户角色，如果是主播角色则跳转到主播详情页面
      if (roles && roles.includes(ROLES.ANCHOR)) {
        targetPath = ROUTES.ANCHOR_PROFILE
      } else if (permissions && permissions.length > 0) {
        // 根据用户权限确定默认页面，使用useMemo避免重复计算
        if (permissions.includes(PERMISSIONS.DASHBOARD.VIEW)) {
          targetPath = ROUTES.DASHBOARD
        } else if (permissions.includes(PERMISSIONS.FINANCIAL.STATS.QUERY)) {
          targetPath = ROUTES.FINANCIAL_DASHBOARD
        } else if (permissions.includes(PERMISSIONS.SYSTEM.USER.LIST)) {
          targetPath = ROUTES.SYSTEM_USER
        } else if (permissions.includes(PERMISSIONS.SYSTEM.VIEW)) {
          targetPath = ROUTES.SYSTEM
        } else {
          // 如果没有任何权限，跳转到仪表板（会显示权限不足）
          targetPath = ROUTES.DASHBOARD
        }
      } else {
        // 权限还未加载完成，跳转到仪表板
        targetPath = ROUTES.DASHBOARD
      }
    }

    console.log('已认证，重定向到:', targetPath, '用户:', user?.username, '权限:', permissions?.length)
    return <Navigate to={targetPath} replace />
  }

  return <>{children}</>
}

/**
 * 权限守卫组件
 */
interface PermissionGuardProps {
  children: React.ReactNode
  permissions?: string[]
  roles?: string[]
  fallback?: React.ReactNode
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permissions = [],
  roles = [],
  fallback = <div className="text-center py-8 text-muted-foreground">没有权限访问此页面</div>,
}) => {
  const { hasAllPermissions, hasAnyRole, isSuperAdmin } = usePermission()

  // 首先检查是否为超级管理员
  const superAdmin = isSuperAdmin()

  // 超级管理员直接通过所有权限检查
  if (superAdmin) {
    console.log('🛡️ [PermissionGuard] 超级管理员，直接通过')
    return <>{children}</>
  }

  // 检查权限
  const hasPermission = permissions.length === 0 || hasAllPermissions(permissions)
  console.log('🛡️ [PermissionGuard] 权限检查结果:', hasPermission)

  // 检查角色
  const hasRole = roles.length === 0 || hasAnyRole(roles)
  console.log('🛡️ [PermissionGuard] 角色检查结果:', hasRole)

  // 如果没有权限或角色，显示fallback
  if (!hasPermission || !hasRole) {
    console.log('🛡️ [PermissionGuard] 权限验证失败，显示fallback')
    return <>{fallback}</>
  }

  console.log('🛡️ [PermissionGuard] 权限验证通过，渲染子组件')
  return <>{children}</>
}

/**
 * 路由权限守卫Hook
 */
export const useRouteGuard = () => {
  const location = useLocation()
  const { setBreadcrumbs } = useAppStore()

  /**
   * 检查路由权限 - 复用 usePermission 中的权限检查逻辑
   */
  // 获取权限检查函数
  const { hasAllPermissions, hasAnyRole } = usePermission()

  const checkRoutePermission = (
    routePermissions?: string[],
    routeRoles?: string[]
  ): boolean => {
    // 如果没有权限要求，允许访问
    if (!routePermissions && !routeRoles) {
      return true
    }

    // 检查权限（使用统一的权限检查逻辑）
    if (routePermissions && routePermissions.length > 0) {
      if (!hasAllPermissions(routePermissions)) {
        return false
      }
    }

    // 检查角色
    if (routeRoles && routeRoles.length > 0) {
      if (!hasAnyRole(routeRoles)) {
        return false
      }
    }

    return true
  }

  /**
   * 更新面包屑导航
   */
  const updateBreadcrumbs = (breadcrumbs: Array<{ title: string; path?: string }>) => {
    setBreadcrumbs(breadcrumbs)
  }

  return {
    currentPath: location.pathname,
    checkRoutePermission,
    updateBreadcrumbs,
  }
}

/**
 * 页面标题守卫Hook
 */
export const usePageTitle = (title?: string) => {
  useEffect(() => {
    if (title) {
      document.title = `${title} - 管理系统`
    } else {
      document.title = '管理系统'
    }
  }, [title])
}

/**
 * 路由变化监听Hook
 */
export const useRouteChange = () => {
  const location = useLocation()
  const { clearGlobalError } = useAppStore()

  useEffect(() => {
    // 路由变化时清除全局错误
    clearGlobalError()
    
    // 滚动到顶部
    window.scrollTo(0, 0)
  }, [location.pathname, clearGlobalError])

  return location
}

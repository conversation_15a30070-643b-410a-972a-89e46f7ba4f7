/**
 * 运营模块API服务
 * 
 * 提供运营数据相关的API调用功能，支持手动分页查询
 */

import { httpClient } from './request'
import type { OperationsStats } from '@/components/operations/StatsCards'

// ==================== 类型定义 ====================
/**
 * 分页请求基类
 */
export interface PageRequest {
  pageNum: number
  pageSize: number
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  records: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

/**
 * 主播查询请求
 */
export interface AnchorQueryRequest extends PageRequest {
  nickname?: string
  status?: number
  identity?: number
  registerStartTime?: number
  registerEndTime?: number
  isAuth?: number
  phone?: string
}

/**
 * 主播列表响应
 */
export interface AnchorListResponse {
  id: number
  nickname: string
  username: string
  phone: string
  userimage?: string
  state: number
  identity: number
  isauth: number
  coin: number
  key: string
  createTime: number
  lastLoginTime?: number
  lastLoginIp?: string
  inviteCode: string
  level: number
  exp: number
  subUserCount: number
  managerName: string
  managerId: number
}

/**
 * 下级用户查询请求
 */
export interface SubUserQueryRequest extends PageRequest {
  nickname?: string
  username?: string
  phone?: string
  state?: number
  isAuth?: number
  registerStartTime?: number
  registerEndTime?: number
  hasFirstRecharge?: boolean
  minRechargeAmount?: number
  maxRechargeAmount?: number
}

/**
 * 下级用户响应
 */
export interface SubUserResponse {
  id: number
  nickname: string
  username: string
  phone: string
  userimage?: string
  state: number
  isauth: number
  coin: number
  key: number
  createTime: number
  lastLoginTime?: number
  lastLoginIp?: string
  level: number
  exp: number
  totalRecharge: number
  totalConsume: number
  firstRechargeTime?: number
  firstRechargeAmount?: number
  hasFirstRecharge?: boolean
  inviteUser?: number
}

/**
 * 充值查询请求
 */
export interface RechargeQueryRequest extends PageRequest {
  orderId?: string
  payId?: string
  startTime?: number
  endTime?: number
  minAmount?: number
  maxAmount?: number
  state?: number
  isFirstRecharge?: boolean
}

/**
 * 充值详情响应
 */
export interface RechargeDetailResponse {
  id: string
  payid: string
  uid: number
  nickname: string
  amount: number
  coin: number
  state: number
  stateDesc: string
  createTime: number
  updateTime: number
  isFirstRecharge: number
  paymentMethod: string
}

/**
 * 消费查询请求
 */
export interface ConsumeQueryRequest extends PageRequest {
  orderId?: string
  startTime?: number
  endTime?: number
  minAmount?: number
  maxAmount?: number
  info?: string
}

/**
 * 消费详情响应
 */
export interface ConsumeDetailResponse {
  id: string
  uid: number
  nickname: string
  amount: number
  balance: number
  time: number
  info: string
}

// ==================== API服务类 ====================

/**
 * 运营数据API服务类
 */
export class OperationsService {
  
  /**
   * 获取主播列表
   * 支持分页、搜索和筛选功能
   * 
   * @param params 查询参数
   * @returns 分页的主播列表
   */
  static async getAnchorList(params: AnchorQueryRequest): Promise<PageResult<AnchorListResponse>> {
    return await httpClient.post<PageResult<AnchorListResponse>>(
      '/operations/anchors',
      params
    )
  }
  
  /**
   * 获取主播下级用户列表
   * 支持分页、搜索和筛选功能
   * 
   * @param anchorId 主播ID
   * @param params 查询参数
   * @returns 分页的下级用户列表
   */
  static async getSubUsers(
    anchorId: number,
    params: SubUserQueryRequest
  ): Promise<PageResult<SubUserResponse>> {
    return await httpClient.post<PageResult<SubUserResponse>>(
      `/operations/anchors/${anchorId}/users`,
      params
    )
  }
  
  /**
   * 获取用户充值详情
   * 支持分页、搜索和筛选功能
   * 
   * @param userId 用户ID
   * @param params 查询参数
   * @returns 分页的充值详情列表
   */
  static async getRechargeDetails(
    userId: number,
    params: RechargeQueryRequest
  ): Promise<PageResult<RechargeDetailResponse>> {
    return await httpClient.post<PageResult<RechargeDetailResponse>>(
      `/operations/users/${userId}/recharge`,
      params
    )
  }
  
  /**
   * 获取用户消费详情
   * 支持分页、搜索和筛选功能
   * 
   * @param userId 用户ID
   * @param params 查询参数
   * @returns 分页的消费详情列表
   */
  static async getConsumeDetails(
    userId: number,
    params: ConsumeQueryRequest
  ): Promise<PageResult<ConsumeDetailResponse>> {
    return await httpClient.post<PageResult<ConsumeDetailResponse>>(
      `/operations/users/${userId}/consume`,
      params
    )
  }

  /**
   * 获取主播详细统计数据（管理员使用，直接使用vim_user ID）
   * 调用存储过程获取主播的详细业务统计信息
   *
   * @param anchorId 主播ID（vim_user表的ID，必须是正整数）
   * @param startTime 开始时间（时间戳，可选）
   * @param endTime 结束时间（时间戳，可选）
   * @returns 主播统计数据
   */
  static async getAnchorStats(
    anchorId: number,
    startTime?: number,
    endTime?: number
  ): Promise<any> {
    // 参数验证
    if (!anchorId || typeof anchorId !== 'number' || anchorId <= 0) {
      throw new Error(`无效的主播ID: ${anchorId}，必须是正整数`)
    }

    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }

    const url = `/operations/anchors/${anchorId}/stats${params.toString() ? '?' + params.toString() : ''}`

    console.log('发送主播统计API请求:', { url, anchorId, startTime, endTime })

    return await httpClient.get(url)
  }

  /**
   * 获取当前用户的主播统计数据（主播用户使用，自动使用当前登录用户的sys_user ID）
   * 调用存储过程获取当前登录主播的详细业务统计信息
   *
   * @param startTime 开始时间（时间戳，可选）
   * @param endTime 结束时间（时间戳，可选）
   * @returns 主播统计数据
   */
  static async getCurrentUserAnchorStats(
    startTime?: number,
    endTime?: number
  ): Promise<any> {
    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }

    const url = `/operations/profile/stats${params.toString() ? '?' + params.toString() : ''}`
    return await httpClient.get(url)
  }

  /**
   * 获取主播首充统计数据（管理员使用，直接使用vim_user ID）
   * 获取主播下级用户的首充人数和转化率统计
   *
   * @param anchorId 主播ID（vim_user表的ID，必须是正整数）
   * @param startTime 开始时间（时间戳，可选）
   * @param endTime 结束时间（时间戳，可选）
   * @returns 首充统计数据
   */
  static async getFirstRechargeStats(
    anchorId: number,
    startTime?: number,
    endTime?: number
  ): Promise<any> {
    // 参数验证
    if (!anchorId || typeof anchorId !== 'number' || anchorId <= 0) {
      throw new Error(`无效的主播ID: ${anchorId}，必须是正整数`)
    }

    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }

    const url = `/operations/anchors/${anchorId}/first-recharge-stats${params.toString() ? '?' + params.toString() : ''}`

    console.log('发送首充统计API请求:', { url, anchorId, startTime, endTime })

    return await httpClient.get(url)
  }

  /**
   * 获取当前用户的主播首充统计数据（主播用户使用，自动使用当前登录用户的sys_user ID）
   * 获取当前登录主播下级用户的首充人数和转化率统计
   *
   * @param startTime 开始时间（时间戳，可选）
   * @param endTime 结束时间（时间戳，可选）
   * @returns 首充统计数据
   */
  static async getCurrentUserFirstRechargeStats(
    startTime?: number,
    endTime?: number
  ): Promise<any> {
    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }

    const url = `/operations/profile/first-recharge-stats${params.toString() ? '?' + params.toString() : ''}`
    return await httpClient.get(url)
  }

  /**
   * 获取运营统计数据
   * 获取总主播数、活跃主播数、总用户数等关键指标
   */
  static async getOperationsStats(startTime?: number, endTime?: number): Promise<OperationsStats> {
    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }

    const url = `/operations/stats${params.toString() ? '?' + params.toString() : ''}`
    return await httpClient.get<OperationsStats>(url)
  }
}

/**
 * 运营数据工具类
 */
export class OperationsUtils {
  /**
   * 日期转时间戳
   */
  static dateToTimestamp(date: Date): number {
    return Math.floor(date.getTime() / 1000)
  }

  /**
   * 格式化金额
   */
  static formatAmount(amount: number | string): string {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  /**
   * 格式化时间戳
   */
  static formatTimestamp(timestamp: number): string {
    if (!timestamp) return '-'
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
  }

  /**
   * 获取用户状态文本
   */
  static getUserStateText(state: number): string {
    switch (state) {
      case 1: return '正常'
      case 2: return '禁用'
      default: return '未知'
    }
  }

  /**
   * 获取用户身份文本
   */
  static getUserIdentityText(identity: number): string {
    switch (identity) {
      case 1: return '普通用户'
      case 2: return '主播'
      case 3: return '代理'
      default: return '未知'
    }
  }

  /**
   * 获取认证状态文本
   */
  static getAuthStatusText(isauth: number): string {
    switch (isauth) {
      case 0: return '未实名'
      case 1: return '已实名'
      default: return '未知'
    }
  }

  /**
   * 获取用户完整手机号
   * @param userId 用户ID
   * @returns 完整的手机号
   */
  static async getUserFullPhone(userId: number): Promise<string> {
    console.log('🚀 发送获取用户完整手机号请求:', { userId })
    const response = await httpClient.get<string>(`/operations/users/${userId}/phone`)
    console.log('✅ 获取用户完整手机号响应:', response)
    return response
  }
}


import { httpClient } from './request'

/**
 * 用户同步预览数据
 */
interface UserSyncPreviewData {
  previewId: string
  totalCount: number
  newUserCount: number
  existingUserCount: number
  invalidUserCount: number
  usersToSync: VimUser[]
  existingUsers: Record<string, UserConflictInfo>
  invalidUsers: UserValidationError[]
  previewTime: number
  sourceInfo: {
    sourceTable: string
    queryTime: number
    queryCondition: string
    dataVersion: string
  }
}

/**
 * VIM用户数据
 */
 interface VimUser {
  username: string
  phone: string
  identity: number
  email?: string
}

/**
 * 用户冲突信息
 */
interface UserConflictInfo {
  conflictType: 'PHONE_CONFLICT' | 'USERNAME_CONFLICT' | 'EMAIL_CONFLICT' | 'MULTIPLE_CONFLICT' | 'DATA_INCONSISTENT'
  existingUser: any
  syncUser: VimUser
  conflictFields: string[]
  conflictDescription: string
  suggestedAction: string
  autoResolvable: boolean
}

/**
 * 用户验证错误
 */
 interface UserValidationError {
  user: VimUser
  userIdentifier: string
  errorType: 'REQUIRED_FIELD_MISSING' | 'INVALID_FORMAT' | 'LENGTH_EXCEEDED' | 'INVALID_ENUM_VALUE' | 'BUSINESS_RULE_VIOLATION' | 'DATA_INTEGRITY_ERROR'
  errorFields: string[]
  errorMessage: string
  detailMessage?: string
  fixSuggestion?: string
  errorLevel: 'FATAL' | 'ERROR' | 'WARNING' | 'INFO'
  ignorable: boolean
}

/**
 * 同步执行请求
 */
interface UserSyncExecuteRequest {
  previewId: string
  selectedUserPhones?: string[]
  /** 事务模式：STRICT(推荐，性能最佳) | LENIENT(兼容模式) */
  transactionMode: 'STRICT' | 'LENIENT'
  batchSize?: number
  skipInvalidUsers?: boolean
  forceOverwrite?: boolean
  conflictStrategy?: 'SKIP' | 'OVERWRITE' | 'MERGE' | 'CREATE_NEW'
  async?: boolean
  remark?: string
}

/**
 * 同步任务状态
 */
 interface UserSyncTaskStatus {
  taskId: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'TIMEOUT'
  currentStep: 'FETCH_AND_VALIDATE' | 'PREPROCESS_USERS' | 'INSERT_USERS' | 'ASSIGN_ROLES' | 'PROCESS_AGENT_DEPTS' | 'COMPLETE_CLEANUP'
  totalSteps: number
  currentStepIndex: number
  progressPercentage: number
  startTime: number
  endTime?: number
  duration?: number
  currentMessage: string
  errorMessage?: string
  errorDetail?: string
  stepHistory: Array<{
    step: string
    status: string
    startTime: number
    endTime?: number
    duration?: number
    message: string
    error?: string
  }>
  syncResult?: any
  stepDetails?: {
    totalUsers: number
    processedUsers: number
    successUsers: number
    failedUsers: number
    skippedUsers: number
    currentBatch: number
    totalBatches: number
    estimatedRemainingTime?: number
  }
}

/**
 * 分页结果
 */
 interface PageResult<T> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

/**
 * 用户同步服务
 */
export class UserSyncService {
  
  /**
   * 获取同步预览
   */
  static async getSyncPreview(): Promise<UserSyncPreviewData> {
    console.log('🚀 获取用户同步预览')
    const response = await httpClient.get<UserSyncPreviewData>('/system/user/sync/preview')
    console.log('✅ 同步预览响应:', response)
    return response
  }

  /**
   * 获取分页同步预览
   */
  static async getSyncPreviewPaged(
    pageNum: number = 1, 
    pageSize: number = 10, 
    userType?: string
  ): Promise<PageResult<VimUser>> {
    console.log('🚀 获取分页同步预览', { pageNum, pageSize, userType })
    const response = await httpClient.get<PageResult<VimUser>>('/system/user/sync/preview/paged', {
      params: { pageNum, pageSize, userType }
    })
    console.log('✅ 分页预览响应:', response)
    return response
  }

  /**
   * 验证用户数据
   */
  static async validateUsers(users: VimUser[]): Promise<UserValidationError[]> {
    console.log('🚀 验证用户数据', users.length)
    const response = await httpClient.post<UserValidationError[]>('/system/user/sync/validate', users)
    console.log('✅ 验证结果:', response)
    return response
  }

  /**
   * 检测用户冲突
   */
  static async detectConflicts(users: VimUser[]): Promise<UserConflictInfo[]> {
    console.log('🚀 检测用户冲突', users.length)
    const response = await httpClient.post<UserConflictInfo[]>('/system/user/sync/conflicts', users)
    console.log('✅ 冲突检测结果:', response)
    return response
  }

  /**
   * 异步执行同步
   * 优化：增加超时时间，确保大数据量时不会超时
   */
  static async executeSyncAsync(request: UserSyncExecuteRequest): Promise<string> {
    console.log('🚀 异步执行同步', request)
    const response = await httpClient.post<string>('/system/user/sync/execute/async', request, {
      timeout: 30000, // 30秒超时，给异步任务创建足够的时间
      skipToast: false // 确保显示成功/失败提示
    })
    console.log('✅ 异步同步任务ID:', response)
    return response
  }

  /**
   * 同步执行同步
   */
  static async executeSyncSync(request: UserSyncExecuteRequest): Promise<any> {
    console.log('🚀 同步执行同步', request)
    const response = await httpClient.post<any>('/system/user/sync/execute/sync', request)
    console.log('✅ 同步执行结果:', response)
    return response
  }

  /**
   * 查询同步任务状态
   */
  static async getSyncTaskStatus(taskId: string): Promise<UserSyncTaskStatus> {
    console.log('🚀 查询任务状态', taskId)
    const response = await httpClient.get<UserSyncTaskStatus>(`/system/user/sync/status/${taskId}`)
    console.log('✅ 任务状态:', response)
    return response
  }

  /**
   * 取消同步任务
   */
  static async cancelSyncTask(taskId: string): Promise<boolean> {
    console.log('🚀 取消同步任务', taskId)
    const response = await httpClient.post<boolean>(`/system/user/sync/cancel/${taskId}`)
    console.log('✅ 取消结果:', response)
    return response
  }

  /**
   * 获取预览数据
   */
  static async getPreviewData(previewId: string): Promise<UserSyncPreviewData> {
    console.log('🚀 获取预览数据', previewId)
    const response = await httpClient.get<UserSyncPreviewData>(`/system/user/sync/preview/${previewId}`)
    console.log('✅ 预览数据:', response)
    return response
  }

  /**
   * 获取同步历史
   */
  static async getSyncHistory(pageNum: number = 1, pageSize: number = 10): Promise<PageResult<UserSyncTaskStatus>> {
    console.log('🚀 获取同步历史', { pageNum, pageSize })
    const response = await httpClient.get<PageResult<UserSyncTaskStatus>>('/system/user/sync/history', {
      params: { pageNum, pageSize }
    })
    console.log('✅ 同步历史:', response)
    return response
  }

  /**
   * 清理过期缓存
   */
  static async cleanupExpiredCaches(): Promise<string> {
    console.log('🚀 清理过期缓存')
    const response = await httpClient.post<string>('/system/user/sync/cleanup')
    console.log('✅ 清理结果:', response)
    return response
  }
}

// 导出类型定义
export type {
  UserSyncPreviewData,
  VimUser,
  UserConflictInfo,
  UserValidationError,
  UserSyncExecuteRequest,
  UserSyncTaskStatus,
  PageResult
}

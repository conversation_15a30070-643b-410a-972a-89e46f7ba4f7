/**
 * 主播数据调试工具
 * 
 * 用于验证主播ID传递和数据完整性
 */

import type { AnchorListResponse } from '@/pages/yunying/types/operations'

/**
 * 验证主播数据的完整性
 */
export function validateAnchorData(anchor: AnchorListResponse | null): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  if (!anchor) {
    errors.push('主播数据为空')
    return { isValid: false, errors, warnings }
  }

  // 验证必需字段
  if (!anchor.id) {
    errors.push('主播ID为空')
  } else if (typeof anchor.id !== 'number') {
    errors.push(`主播ID类型错误，期望number，实际${typeof anchor.id}`)
  } else if (anchor.id <= 0) {
    errors.push(`主播ID必须是正整数，当前值: ${anchor.id}`)
  }

  if (!anchor.nickname) {
    warnings.push('主播昵称为空')
  }

  if (!anchor.username) {
    warnings.push('主播用户名为空')
  }

  // 验证状态字段
  if (anchor.state === undefined || anchor.state === null) {
    warnings.push('主播状态未定义')
  }

  if (anchor.identity === undefined || anchor.identity === null) {
    warnings.push('主播身份类型未定义')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 打印主播数据调试信息
 */
export function debugAnchorData(anchor: AnchorListResponse | null, context: string = '') {
  console.group(`🔍 主播数据调试 ${context ? `- ${context}` : ''}`)
  
  const validation = validateAnchorData(anchor)
  
  console.log('主播数据:', anchor)
  console.log('验证结果:', validation)
  
  if (anchor) {
    console.log('关键字段检查:')
    console.log('  - ID:', anchor.id, typeof anchor.id)
    console.log('  - 昵称:', anchor.nickname)
    console.log('  - 用户名:', anchor.username)
    console.log('  - 状态:', anchor.state)
    console.log('  - 身份:', anchor.identity)
  }
  
  if (validation.errors.length > 0) {
    console.error('❌ 错误:', validation.errors)
  }
  
  if (validation.warnings.length > 0) {
    console.warn('⚠️ 警告:', validation.warnings)
  }
  
  if (validation.isValid) {
    console.log('✅ 主播数据验证通过')
  }
  
  console.groupEnd()
  
  return validation
}

/**
 * 验证API调用参数
 */
export function validateApiParams(anchorId: number, startTime?: number, endTime?: number) {
  console.group('🔍 API参数验证')
  
  const errors: string[] = []
  
  console.log('参数值:')
  console.log('  - anchorId:', anchorId, typeof anchorId)
  console.log('  - startTime:', startTime, typeof startTime)
  console.log('  - endTime:', endTime, typeof endTime)
  
  // 验证anchorId
  if (!anchorId) {
    errors.push('anchorId为空')
  } else if (typeof anchorId !== 'number') {
    errors.push(`anchorId类型错误，期望number，实际${typeof anchorId}`)
  } else if (anchorId <= 0) {
    errors.push(`anchorId必须是正整数，当前值: ${anchorId}`)
  } else if (!Number.isInteger(anchorId)) {
    errors.push(`anchorId必须是整数，当前值: ${anchorId}`)
  }
  
  // 验证时间参数
  if (startTime !== undefined) {
    if (typeof startTime !== 'number') {
      errors.push(`startTime类型错误，期望number，实际${typeof startTime}`)
    } else if (startTime <= 0) {
      errors.push(`startTime必须是正数，当前值: ${startTime}`)
    }
  }
  
  if (endTime !== undefined) {
    if (typeof endTime !== 'number') {
      errors.push(`endTime类型错误，期望number，实际${typeof endTime}`)
    } else if (endTime <= 0) {
      errors.push(`endTime必须是正数，当前值: ${endTime}`)
    }
  }
  
  if (startTime && endTime && startTime >= endTime) {
    errors.push(`开始时间必须小于结束时间，startTime: ${startTime}, endTime: ${endTime}`)
  }
  
  const isValid = errors.length === 0
  
  if (errors.length > 0) {
    console.error('❌ 参数验证失败:', errors)
  } else {
    console.log('✅ 参数验证通过')
  }
  
  console.groupEnd()
  
  return { isValid, errors }
}

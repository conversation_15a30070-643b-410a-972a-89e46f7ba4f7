/**
 * 权限控制工具函数
 * 
 * 提供标准化的权限控制逻辑和工具函数
 * 统一重构版本 - 为所有模块提供一致的权限控制体验
 */

import { PERMISSIONS } from '@/constants/permissions'
import React from "react";

/**
 * 权限控制配置接口
 */
export interface PermissionConfig {
  /** 页面查看权限 */
  view: string
  /** 列表查看权限 */
  list: string
  /** 详情查看权限 */
  detail?: string
  /** 新增权限 */
  add: string
  /** 编辑权限 */
  edit: string
  /** 删除权限 */
  delete: string
  /** 导入权限 */
  import?: string
  /** 导出权限 */
  export?: string
  /** 其他自定义权限 */
  [key: string]: string | undefined
}

/**
 * 模块权限配置映射
 */
export const MODULE_PERMISSIONS: Record<string, PermissionConfig> = {
  // 用户管理模块权限
  user: {
    view: PERMISSIONS.SYSTEM.VIEW,
    list: PERMISSIONS.SYSTEM.USER.LIST,
    detail: PERMISSIONS.SYSTEM.USER.VIEW,
    add: PERMISSIONS.SYSTEM.USER.ADD,
    edit: PERMISSIONS.SYSTEM.USER.EDIT,
    delete: PERMISSIONS.SYSTEM.USER.DELETE,
    sync: PERMISSIONS.SYSTEM.USER.SYNC,
    resetPassword: PERMISSIONS.SYSTEM.USER.RESET_PASSWORD,
  },
  
  // 角色管理模块权限
  role: {
    view: PERMISSIONS.SYSTEM.VIEW,
    list: PERMISSIONS.SYSTEM.ROLE.LIST,
    detail: PERMISSIONS.SYSTEM.ROLE.VIEW,
    add: PERMISSIONS.SYSTEM.ROLE.ADD,
    edit: PERMISSIONS.SYSTEM.ROLE.EDIT,
    delete: PERMISSIONS.SYSTEM.ROLE.DELETE,
    assignPermissions: PERMISSIONS.SYSTEM.ROLE.ASSIGN_PERMISSIONS,
    auth: PERMISSIONS.SYSTEM.ROLE.AUTH,
  },
  
  // 部门管理模块权限
  dept: {
    view: PERMISSIONS.SYSTEM.VIEW,
    list: PERMISSIONS.SYSTEM.DEPT.LIST,
    detail: PERMISSIONS.SYSTEM.DEPT.VIEW,
    add: PERMISSIONS.SYSTEM.DEPT.ADD,
    edit: PERMISSIONS.SYSTEM.DEPT.EDIT,
    delete: PERMISSIONS.SYSTEM.DEPT.DELETE,
  },
  
  // 权限管理模块权限
  permission: {
    view: PERMISSIONS.SYSTEM.VIEW,
    list: 'system:permission:list', // 占位符，数据库中没有对应功能
    detail: 'system:permission:view', // 占位符，数据库中没有对应功能
    add: PERMISSIONS.SYSTEM.PERMISSION.ADD,
    edit: PERMISSIONS.SYSTEM.PERMISSION.EDIT,
    delete: PERMISSIONS.SYSTEM.PERMISSION.DELETE,
  },
  
  // 菜单管理模块权限
  menu: {
    view: PERMISSIONS.MENU.VIEW,
    list: PERMISSIONS.MENU.LIST,
    detail: PERMISSIONS.MENU.QUERY,
    add: PERMISSIONS.MENU.ADD,
    edit: PERMISSIONS.MENU.EDIT,
    delete: PERMISSIONS.MENU.DELETE,
    import: PERMISSIONS.MENU.IMPORT,
    export: PERMISSIONS.MENU.EXPORT,
  },

  // 租户管理模块权限
  tenant: {
    view: PERMISSIONS.SYSTEM.VIEW,
    list: PERMISSIONS.SYSTEM.TENANT.LIST,
    detail: PERMISSIONS.SYSTEM.TENANT.VIEW,
    add: PERMISSIONS.SYSTEM.TENANT.ADD,
    edit: PERMISSIONS.SYSTEM.TENANT.EDIT,
    delete: PERMISSIONS.SYSTEM.TENANT.DELETE,
    query: PERMISSIONS.SYSTEM.TENANT.QUERY,
  },

  // 运营管理模块权限
  operations: {
    view: PERMISSIONS.OPERATIONS.VIEW,
    list: PERMISSIONS.OPERATIONS.ANCHOR.LIST,
    detail: PERMISSIONS.OPERATIONS.ANCHOR.VIEW,
    add: 'operations:add', // 占位符权限，运营管理主要是查看功能
    edit: 'operations:edit', // 占位符权限，运营管理主要是查看功能
    delete: 'operations:delete', // 占位符权限，运营管理主要是查看功能
    query: PERMISSIONS.OPERATIONS.ANCHOR.QUERY,
    stats: PERMISSIONS.OPERATIONS.ANCHOR.STATS,
    firstRecharge: PERMISSIONS.OPERATIONS.ANCHOR.FIRST_RECHARGE,
    subUsers: PERMISSIONS.OPERATIONS.ANCHOR.SUB_USERS,
    users: PERMISSIONS.OPERATIONS.ANCHOR.USERS,
    consume: PERMISSIONS.OPERATIONS.USER.CONSUME,
    recharge: PERMISSIONS.OPERATIONS.USER.RECHARGE,
  },
}

/**
 * 获取模块权限配置
 */
export const getModulePermissions = (module: string): PermissionConfig => {
  const permissions = MODULE_PERMISSIONS[module]
  if (!permissions) {
    console.warn(`未找到模块 ${module} 的权限配置`)
    return {
      view: '',
      list: '',
      add: '',
      edit: '',
      delete: '',
    }
  }
  return permissions
}

/**
 * 权限控制操作类型
 */
export type PermissionAction = 
  | 'view' 
  | 'list' 
  | 'detail' 
  | 'add' 
  | 'edit' 
  | 'delete' 
  | 'import' 
  | 'export'
  | string // 支持自定义操作

/**
 * 获取模块特定操作的权限
 */
export const getModulePermission = (
  module: string, 
  action: PermissionAction
): string => {
  const permissions = getModulePermissions(module)
  return permissions[action] || ''
}

/**
 * 批量获取模块权限
 */
export const getModulePermissionsBatch = (
  module: string, 
  actions: PermissionAction[]
): string[] => {
  return actions.map(action => getModulePermission(module, action)).filter(Boolean)
}

/**
 * 权限控制按钮配置
 */
export interface PermissionButtonConfig {
  /** 按钮文本 */
  text: string
  /** 按钮图标 */
  icon?: React.ComponentType<any>
  /** 按钮变体 */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  /** 按钮大小 */
  size?: 'default' | 'sm' | 'lg' | 'icon'
  /** 需要的权限 */
  permissions: string[]
  /** 点击处理函数 */
  onClick: () => void
  /** 是否禁用 */
  disabled?: boolean
  /** 无权限时的行为 */
  noPermissionBehavior?: 'hide' | 'disable' | 'show'
  /** 悬停提示文本 */
  title?: string
  /** 自定义样式类名 */
  className?: string
}

/**
 * 标准化的模块操作按钮配置
 */
export const createModuleButtonConfigs = (
  module: string,
  handlers: {
    onAdd?: () => void
    onEdit?: (item: any) => void
    onDelete?: (item: any) => void
    onImport?: () => void
    onExport?: () => void
    [key: string]: ((item?: any) => void) | undefined
  }
): Record<string, PermissionButtonConfig> => {
  const permissions = getModulePermissions(module)
  
  const configs: Record<string, PermissionButtonConfig> = {}
  
  // 新增按钮
  if (handlers.onAdd && permissions.add) {
    configs.add = {
      text: '新增',
      variant: 'default',
      permissions: [permissions.add],
      onClick: handlers.onAdd,
    }
  }
  
  // 编辑按钮
  if (handlers.onEdit && permissions.edit) {
    configs.edit = {
      text: '编辑',
      variant: 'outline',
      size: 'sm',
      permissions: [permissions.edit],
      onClick: () => {}, // 需要传入具体项目
    }
  }
  
  // 删除按钮
  if (handlers.onDelete && permissions.delete) {
    configs.delete = {
      text: '删除',
      variant: 'destructive',
      size: 'sm',
      permissions: [permissions.delete],
      onClick: () => {}, // 需要传入具体项目
    }
  }
  
  // 导入按钮
  if (handlers.onImport && permissions.import) {
    configs.import = {
      text: '导入',
      variant: 'outline',
      permissions: [permissions.import],
      onClick: handlers.onImport,
    }
  }
  
  // 导出按钮
  if (handlers.onExport && permissions.export) {
    configs.export = {
      text: '导出',
      variant: 'outline',
      permissions: [permissions.export],
      onClick: handlers.onExport,
    }
  }
  
  return configs
}

/**
 * 权限检查结果
 */
export interface PermissionCheckResult {
  /** 是否有权限 */
  hasPermission: boolean
  /** 缺失的权限列表 */
  missingPermissions: string[]
  /** 是否为超级管理员 */
  isSuperAdmin: boolean
}

/**
 * 权限控制调试信息
 */
export const logPermissionDebug = (
  module: string,
  action: string,
  result: PermissionCheckResult,
  context?: any
) => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔐 权限检查 - ${module}:${action}`)
    console.log('检查结果:', result.hasPermission ? '✅ 通过' : '❌ 拒绝')
    console.log('是否超管:', result.isSuperAdmin ? '✅ 是' : '❌ 否')
    if (result.missingPermissions.length > 0) {
      console.log('缺失权限:', result.missingPermissions)
    }
    if (context) {
      console.log('上下文:', context)
    }
    console.groupEnd()
  }
}

/**
 * 权限控制模式
 */
export type PermissionFallbackMode = 'hide' | 'mask' | 'show' | 'disable';

/**
 * 敏感度级别
 */
export type SensitiveLevel = 'low' | 'medium' | 'high' | 'critical';

/**
 * 统计项权限配置接口
 */
export interface StatsPermissionConfig {
  permission: string;
  fallbackMode: PermissionFallbackMode;
  sensitiveLevel: SensitiveLevel;
  maskValue: string;
}

/**
 * 统计项权限映射配置
 * 定义每个统计项需要的权限和显示策略
 */
export const STATS_PERMISSION_CONFIG = {
  // 财务相关统计
  totalRevenue: {
    permission: PERMISSIONS.OPERATIONS.STATS.VIEW_FINANCIAL,
    fallbackMode: 'hide' as const, // 无权限时隐藏
    sensitiveLevel: 'high' as const,
    maskValue: '***'
  },
  actualProfit: {
    permission: PERMISSIONS.OPERATIONS.STATS.VIEW_PROFIT,
    fallbackMode: 'mask' as const, // 无权限时脱敏显示
    sensitiveLevel: 'critical' as const,
    maskValue: '***'
  },
  pendingAmount: {
    permission: PERMISSIONS.OPERATIONS.STATS.VIEW_PENDING_AMOUNT,
    fallbackMode: 'mask' as const,
    sensitiveLevel: 'medium' as const,
    maskValue: '***'
  },
  shippedAmount: {
    permission: PERMISSIONS.OPERATIONS.STATS.VIEW_SHIPPED_AMOUNT,
    fallbackMode: 'mask' as const,
    sensitiveLevel: 'medium' as const,
    maskValue: '***'
  },
  backpackValue: {
    permission: PERMISSIONS.OPERATIONS.STATS.VIEW_BACKPACK_VALUE,
    fallbackMode: 'hide' as const,
    sensitiveLevel: 'high' as const,
    maskValue: '***'
  },
  // 基础统计（较低敏感度）
  orderCount: {
    permission: PERMISSIONS.OPERATIONS.VIEW,
    fallbackMode: 'show' as const,
    sensitiveLevel: 'low' as const,
    maskValue: ''
  },
  userCount: {
    permission: PERMISSIONS.OPERATIONS.VIEW,
    fallbackMode: 'show' as const,
    sensitiveLevel: 'low' as const,
    maskValue: ''
  }
} as const;

#!/bin/bash

# jCloud项目构建脚本
# 用于构建Docker镜像

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 清理旧镜像
cleanup_images() {
    log_info "清理旧的Docker镜像..."
    
    # 删除旧的jcloud镜像
    docker images | grep "jcloud/" | awk '{print $3}' | xargs -r docker rmi -f || true
    
    # 清理悬空镜像
    docker image prune -f || true
    
    log_success "镜像清理完成"
}

# 构建后端镜像
build_backend() {
    log_info "开始构建后端镜像..."
    
    cd backend
    
    # 检查Maven是否可用
    if ! command -v mvn &> /dev/null; then
        log_warning "Maven未安装，将使用Docker内置Maven进行构建"
    fi
    
    # 构建Docker镜像
    docker build -t jcloud/backend:1.0.0 -t jcloud/backend:latest .
    
    cd ..
    
    log_success "后端镜像构建完成"
}

# 构建前端镜像
build_frontend() {
    log_info "开始构建前端镜像..."
    
    cd frontend
    
    # 检查Node.js是否可用
    if ! command -v node &> /dev/null; then
        log_warning "Node.js未安装，将使用Docker内置Node.js进行构建"
    fi
    
    # 构建生产环境镜像（使用nginx-prod.conf）
    docker build --build-arg NGINX_CONFIG=nginx-prod.conf -t jcloud/frontend:1.0.0 -t jcloud/frontend:latest .
    
    cd ..
    
    log_success "前端镜像构建完成"
}

# 显示构建结果
show_results() {
    log_info "构建完成，镜像列表："
    echo ""
    docker images | grep "jcloud/"
    echo ""
    
    log_info "镜像大小统计："
    echo "后端镜像: $(docker images jcloud/backend:latest --format "table {{.Size}}" | tail -n 1)"
    echo "前端镜像: $(docker images jcloud/frontend:latest --format "table {{.Size}}" | tail -n 1)"
}

# 主函数
main() {
    log_info "开始构建jCloud项目Docker镜像..."
    echo ""
    
    # 检查环境
    check_docker
    
    # 询问是否清理旧镜像
    read -p "是否清理旧的Docker镜像？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_images
    fi
    
    # 构建镜像
    build_backend
    build_frontend
    
    # 显示结果
    show_results
    
    log_success "所有镜像构建完成！"
    log_info "接下来可以运行 ./scripts/deploy.sh 进行部署"
}

# 执行主函数
main "$@"

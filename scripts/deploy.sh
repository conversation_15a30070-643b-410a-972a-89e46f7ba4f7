#!/bin/bash

# jCloud项目部署脚本
# 用于部署到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境配置文件 $ENV_FILE 不存在"
        log_info "请复制 .env.prod.template 为 $ENV_FILE 并配置相应参数"
        exit 1
    fi
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose配置文件 $COMPOSE_FILE 不存在"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查镜像
check_images() {
    log_info "检查Docker镜像..."
    
    if ! docker images | grep -q "jcloud/backend"; then
        log_warning "后端镜像不存在，请先运行构建脚本"
        read -p "是否现在构建镜像？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            ./scripts/build.sh
        else
            log_error "缺少必要的Docker镜像"
            exit 1
        fi
    fi
    
    if ! docker images | grep -q "jcloud/frontend"; then
        log_error "前端镜像不存在，请先运行构建脚本"
        exit 1
    fi
    
    log_success "镜像检查通过"
}

# 检查外部依赖
check_external_services() {
    log_info "检查外部服务连接..."
    
    # 从环境文件读取配置
    source "$ENV_FILE"
    
    # 检查数据库连接
    log_info "检查数据库连接 $DB_HOST:$DB_PORT..."
    if ! timeout 5 bash -c "</dev/tcp/$DB_HOST/$DB_PORT"; then
        log_warning "无法连接到数据库 $DB_HOST:$DB_PORT"
        log_warning "请确保数据库服务正常运行且网络可达"
    else
        log_success "数据库连接正常"
    fi
    
    # 检查Redis连接
    log_info "检查Redis连接 $REDIS_HOST:$REDIS_PORT..."
    if ! timeout 5 bash -c "</dev/tcp/$REDIS_HOST/$REDIS_PORT"; then
        log_warning "无法连接到Redis $REDIS_HOST:$REDIS_PORT"
        log_warning "请确保Redis服务正常运行且网络可达"
    else
        log_success "Redis连接正常"
    fi
}

# 停止旧服务
stop_old_services() {
    log_info "停止旧的服务..."
    
    # 停止并删除容器
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --remove-orphans || true
    
    # 清理悬空容器
    docker container prune -f || true
    
    log_success "旧服务已停止"
}

# 启动服务
start_services() {
    log_info "启动jCloud服务..."
    
    # 启动服务
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待后端服务
    log_info "等待后端服务启动..."
    for i in {1..30}; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend wget --no-verbose --tries=1 --spider http://localhost:8081/api/actuator/health 2>/dev/null; then
            log_success "后端服务已就绪"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "后端服务启动超时"
            show_logs
            exit 1
        fi
        
        echo -n "."
        sleep 2
    done
    
    # 等待前端服务
    log_info "等待前端服务启动..."
    for i in {1..15}; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T frontend wget --no-verbose --tries=1 --spider http://localhost/health 2>/dev/null; then
            log_success "前端服务已就绪"
            break
        fi
        
        if [ $i -eq 15 ]; then
            log_error "前端服务启动超时"
            show_logs
            exit 1
        fi
        
        echo -n "."
        sleep 2
    done
}

# 显示服务状态
show_status() {
    log_info "服务状态："
    echo ""
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    echo ""
    
    # 显示访问地址
    source "$ENV_FILE"
    log_info "访问地址："
    echo "前端地址: http://localhost:${FRONTEND_PORT}"
    echo "后端API: http://localhost:${BACKEND_PORT}/api"
    echo "健康检查: http://localhost:${BACKEND_PORT}/api/actuator/health"
    echo ""
}

# 显示日志
show_logs() {
    log_info "最近的服务日志："
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs --tail=50
}

# 主函数
main() {
    log_info "开始部署jCloud项目到生产环境..."
    echo ""
    
    # 环境检查
    check_environment
    check_images
    check_external_services
    
    # 部署确认
    echo ""
    log_warning "即将部署到生产环境，这将停止现有服务并启动新版本"
    read -p "确认继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 执行部署
    stop_old_services
    start_services
    wait_for_services
    
    # 显示结果
    show_status
    
    log_success "jCloud项目部署完成！"
    log_info "可以使用 ./scripts/stop.sh 停止服务"
    log_info "可以使用 ./scripts/restart.sh 重启服务"
}

# 执行主函数
main "$@"

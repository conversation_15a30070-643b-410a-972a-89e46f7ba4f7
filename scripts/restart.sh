#!/bin/bash

# jCloud项目重启脚本
# 用于重启生产环境服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查配置文件
check_config() {
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose配置文件 $COMPOSE_FILE 不存在"
        exit 1
    fi
    
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境配置文件 $ENV_FILE 不存在"
        exit 1
    fi
}

# 显示当前状态
show_current_status() {
    log_info "当前服务状态："
    echo ""
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps 2>/dev/null || echo "没有运行的服务"
    echo ""
}

# 重启服务
restart_services() {
    log_info "正在重启jCloud服务..."
    
    # 重启服务
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart
    
    log_success "服务重启完成"
}

# 重新部署服务
redeploy_services() {
    log_info "正在重新部署jCloud服务..."
    
    # 停止并删除容器
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --remove-orphans
    
    # 重新启动服务
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    log_success "服务重新部署完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待后端服务
    log_info "等待后端服务启动..."
    for i in {1..30}; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend wget --no-verbose --tries=1 --spider http://localhost:8081/api/actuator/health 2>/dev/null; then
            log_success "后端服务已就绪"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "后端服务启动超时"
            return 1
        fi
        
        echo -n "."
        sleep 2
    done
    
    # 等待前端服务
    log_info "等待前端服务启动..."
    for i in {1..15}; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T frontend wget --no-verbose --tries=1 --spider http://localhost/health 2>/dev/null; then
            log_success "前端服务已就绪"
            break
        fi
        
        if [ $i -eq 15 ]; then
            log_error "前端服务启动超时"
            return 1
        fi
        
        echo -n "."
        sleep 2
    done
    
    return 0
}

# 显示服务状态
show_final_status() {
    log_info "服务状态："
    echo ""
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    echo ""
    
    # 显示访问地址
    source "$ENV_FILE"
    log_info "访问地址："
    echo "前端地址: http://localhost:${FRONTEND_PORT}"
    echo "后端API: http://localhost:${BACKEND_PORT}/api"
    echo "健康检查: http://localhost:${BACKEND_PORT}/api/actuator/health"
    echo ""
}

# 主函数
main() {
    log_info "重启jCloud项目服务..."
    echo ""
    
    # 检查配置
    check_config
    
    # 显示当前状态
    show_current_status
    
    # 询问重启类型
    echo "请选择重启方式："
    echo "1) 快速重启（restart）- 保留容器，仅重启服务"
    echo "2) 完全重启（redeploy）- 删除容器后重新创建"
    echo "3) 取消操作"
    echo ""
    
    read -p "请输入选择 (1-3): " -n 1 -r
    echo ""
    echo ""
    
    case $REPLY in
        1)
            restart_services
            if wait_for_services; then
                show_final_status
                log_success "快速重启完成！"
            else
                log_error "服务启动失败，请检查日志"
                docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs --tail=50
                exit 1
            fi
            ;;
        2)
            redeploy_services
            if wait_for_services; then
                show_final_status
                log_success "完全重启完成！"
            else
                log_error "服务启动失败，请检查日志"
                docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs --tail=50
                exit 1
            fi
            ;;
        3)
            log_info "操作已取消"
            exit 0
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

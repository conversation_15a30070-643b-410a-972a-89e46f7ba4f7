#!/bin/bash

# jCloud项目停止脚本
# 用于停止生产环境服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查配置文件
check_config() {
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose配置文件 $COMPOSE_FILE 不存在"
        exit 1
    fi
    
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境配置文件 $ENV_FILE 不存在"
        exit 1
    fi
}

# 显示当前状态
show_current_status() {
    log_info "当前服务状态："
    echo ""
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps 2>/dev/null || echo "没有运行的服务"
    echo ""
}

# 停止服务
stop_services() {
    log_info "正在停止jCloud服务..."
    
    # 停止服务
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop
    
    log_success "服务已停止"
}

# 删除容器
remove_containers() {
    log_info "正在删除容器..."
    
    # 删除容器
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --remove-orphans
    
    log_success "容器已删除"
}

# 清理资源
cleanup_resources() {
    log_info "清理Docker资源..."
    
    # 清理悬空容器
    docker container prune -f || true
    
    # 清理悬空网络
    docker network prune -f || true
    
    log_success "资源清理完成"
}

# 主函数
main() {
    log_info "停止jCloud项目服务..."
    echo ""
    
    # 检查配置
    check_config
    
    # 显示当前状态
    show_current_status
    
    # 询问操作类型
    echo "请选择操作："
    echo "1) 仅停止服务（保留容器）"
    echo "2) 停止并删除容器"
    echo "3) 停止、删除容器并清理资源"
    echo "4) 取消操作"
    echo ""
    
    read -p "请输入选择 (1-4): " -n 1 -r
    echo ""
    echo ""
    
    case $REPLY in
        1)
            stop_services
            log_info "服务已停止，容器保留。使用 docker-compose start 可以快速重启"
            ;;
        2)
            stop_services
            remove_containers
            log_info "服务已停止，容器已删除。需要重新部署才能启动服务"
            ;;
        3)
            stop_services
            remove_containers
            cleanup_resources
            log_info "服务已停止，容器已删除，资源已清理"
            ;;
        4)
            log_info "操作已取消"
            exit 0
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
    
    echo ""
    log_success "操作完成！"
    
    # 显示最终状态
    log_info "最终状态："
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps 2>/dev/null || echo "没有运行的服务"
}

# 执行主函数
main "$@"
